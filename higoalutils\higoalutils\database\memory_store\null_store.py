# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Null memory store."""

from collections.abc import AsyncGenerator, AsyncIterator
from typing import Any

from higoalutils.database.memory_store.base import MemoryStoreBase


class NullMemoryStore(MemoryStoreBase):
    """Null memory store."""

    async def get(self, key: str):
        """Get value from memory store."""
        return

    async def set(self, key: str, value: Any, expire: int | None = None):
        """Set value in memory store."""
        return

    async def has(self, key: str) -> bool:
        """Check if key exists in memory store."""
        return False

    async def delete(self, *keys: str):
        """Delete key from memory store."""
        return

    async def clear(self):
        """Clear memory store."""
        return

    async def warmup(self):
        """Warmup memory store."""
        return

    async def close(self):
        """Close memory store."""
        return

    async def subscribe(self, channel: str) -> AsyncGenerator[str, None]:
        """Subscribe to channel."""
        if False:
            yield ""
        msg = "This memory store does not support subscribe."
        raise NotImplementedError(msg)

    async def publish(self, channel: str, message: str) -> None:
        """Publish message to channel."""
        return
    
    async def scan_iter(self, match: str = "*") -> AsyncIterator[str]:
        """Scan keys matching pattern."""
        if False:
            yield ""
        msg = "This memory store does not support scan_iter."
        raise NotImplementedError(msg)