import os
import logging
import asyncio
import pandas as pd
from pathlib import Path
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from datetime import datetime
import tushare as ts
import re
from higoalutils.config.load_config import get_config

logger = logging.getLogger(__name__)
_executor = ThreadPoolExecutor(max_workers=2)


class TushareDataFetcher:
    def __init__(self):
        self._cfg = get_config().base_config
        self._data_dir = Path(self._cfg.root_dir) / "database/future"
        self._data_dir.mkdir(parents=True, exist_ok=True)
        self._CSV_PATH_TS = self._data_dir / "ts_data_queried.csv"
        self._Calendar_path = self._data_dir / "trade_calendar.csv"
        self.EXCHANGE_CODE_MAP = {
            "SHFE": "SHF",
            "DCE": "DCE",
            "INE": "INE",
            "CZCE": "ZCE",
            "CFFEX": "CFX",
            "GFEX": "GFE",
        }

        TS_TOKEN = os.getenv("TS_API_KEY")
        if not TS_TOKEN:
            raise RuntimeError("TS_API_KEY environment variable not set.")
        ts.set_token(TS_TOKEN)
        self.api = ts.pro_api()

    async def get_data_ts(self, parameters) -> str:
        return await asyncio.get_running_loop().run_in_executor(
            _executor, self._get_data_sync, parameters
        )

    def _get_data_sync(self, parameters) -> str:
        param_dict = {p.name: p.value for p in parameters.parameters}
        time_step = self.parse_time_step(param_dict)

        if "min" in time_step:
            return self._get_min_kline(param_dict, time_step)
        if "week" in time_step or "month" in time_step:
            return self._get_weekly_monthly_kline(param_dict, time_step)
        else:
            return self._get_daily_kline(param_dict)

    def _get_daily_kline(self, param_dict):
        parameters_ts = self.para_parser_kline(param_dict, 'quick')
        df = self.api.fut_daily(**parameters_ts)
        df = df.sort_values("trade_date", ascending=False)
        df.to_csv(self._CSV_PATH_TS, mode="a", index=False, header=not self._CSV_PATH_TS.exists())
        return self.format_df_as_markdown(df)

    def _get_min_kline(self, param_dict, time_step):
        "历史分钟线数据，实时接口未购买。历史的start_date和end_date要精确到分钟,目前默认从"
        parameters_ts = self.para_parser_kline(param_dict)
        parameters_ts_min = self.para_parse_minute(parameters_ts)
        df = self.api.ft_mins(**parameters_ts_min, freq=time_step)
        df = df.sort_values("trade_date", ascending=False)
        df.to_csv(self._CSV_PATH_TS, mode="a", index=False, header=not self._CSV_PATH_TS.exists())
        return self.format_df_as_markdown(df)

    def _get_weekly_monthly_kline(self, param_dict, time_step):
        return "*暂未实现*"

    def get_calendar_data(self, start_year=2018, end_year=2026):
        exchanges = ["SSE", "SZSE", "CFFEX", "SHFE", "DCE", "CZCE"]
        start_date = f"{start_year}0101"
        end_date = f"{end_year}1231"
        dfs = []
        for exch in exchanges:
            df = self.api.trade_cal(exchange=exch, start_date=start_date, end_date=end_date)
            if not df.empty:
                dfs.append(df)
        if not dfs:
            return False
        df_all = pd.concat(dfs, ignore_index=True)
        df_all.to_csv(self._Calendar_path, index=False)
        print(f"Calendar data saved to {self._Calendar_path}")
        return True

    def format_df_as_markdown(self, df: pd.DataFrame, max_columns_per_row: int = 15, max_rows: int = 5) -> str:
        if df.empty:
            return "*空表格*"

        markdown_lines = []
        total_cols = df.shape[1]
        col_names = list(df.columns)
        display_df = df.head(max_rows)

        for i in range(0, total_cols, max_columns_per_row):
            chunk_cols = col_names[i:i + max_columns_per_row]
            header = "|" + "|".join(chunk_cols) + "|"
            divider = "|" + "|".join(["---"] * len(chunk_cols)) + "|"
            markdown_lines.extend([header, divider])
            for _, row in display_df.iterrows():
                values = "|" + "|".join(str(row[col]) for col in chunk_cols) + "|"
                markdown_lines.append(values)
            markdown_lines.append("")

        return "\n".join(markdown_lines)

    def para_parser_kline(self, param_dict, mode=None) -> dict:
        exchange_code = param_dict.get('exchange_code')
        tushare_exchange = self.EXCHANGE_CODE_MAP.get(exchange_code, exchange_code)
        symbol = param_dict.get('symbol')
        start_date = param_dict.get('fromdate')
        end_date = param_dict.get('todate')
        if start_date:
            start_date = start_date.replace("-", "")
        if end_date:
            end_date = end_date.replace("-", "")
        ts_code = self.parse_symbol(symbol, tushare_exchange, end_date, mode)
        parsed = {
            'ts_code': ts_code,
            'start_date': start_date,
            'end_date': end_date
        }
        return {k: v for k, v in parsed.items() if v is not None}

    def para_parse_minute(self, param_dict: dict) -> dict:
        """
        将 start_date / end_date YYYYMMDD）转换为带时间的 start_date / end_date（格式 YYYY-MM-DD HH:MM:SS）。
        同时保留 param_dict 其他字段。
        """
        start_date_raw = param_dict.get('start_date')
        end_date_raw = param_dict.get('end_date')

        try:
            start_date = datetime.strptime(start_date_raw, "%Y%m%d").strftime("%Y-%m-%d 09:00:00")
        except (TypeError, ValueError):
            start_date = ""

        try:
            end_date = datetime.strptime(end_date_raw, "%Y%m%d").strftime("%Y-%m-%d 15:00:00")
        except (TypeError, ValueError):
            end_date = ""
        param_dict["start_date"] = start_date
        param_dict["end_date"] = end_date
        return param_dict
            
        
        
    def parse_symbol(self, symbol: str, exchange_code: str, date: str, mode=None) -> str | None:
        if not symbol or not exchange_code:
            return None
        if "主力合约" in symbol:
            #TODO: 研究直接传prefix使用默认主力合约vs找出主力合约数传入的速度
            prefix = symbol.replace("主力合约", "").strip()
            if mode == "quick" and date == datetime.today().strftime("%Y%m%d"):
                return f"{prefix}.{exchange_code}"
            return self.find_main_contract(prefix, exchange_code, date)
        return f"{symbol}.{exchange_code}"

    def parse_time_step(self, param_dicts) -> str:
        time_step = param_dicts.get('time_step')
        if not time_step:
            return ""
        
        time_step = time_step.replace(" ", "").lower()
        match = re.match(r"(\d+)([a-zA-Z]+)", time_step)
        if not match:
            return ""
        
        num_str, unit_str = match.groups()
        num = int(num_str)

        minute_units = {"min", "m", "minute", "minutes"}
        hour_units = {"h", "hr", "hour", "hours"}
        passthrough_units = {
            "day": "day",
            "days": "day",
            "week": "week",
            "weeks": "week",
            "month": "month",
            "months": "month",
        }

        if unit_str in minute_units:
            return f"{num}min"
        elif unit_str in hour_units:
            return f"{num * 60}min"
        elif unit_str in passthrough_units:
            return f"{num}{passthrough_units[unit_str]}"
        else:
            return ""
        

    def find_main_contract(self, prefix: str, exchange_code: str, date: str) -> str | None:
        try:
            ts_code = f"{prefix}.{exchange_code}"
            df_map = self.api.fut_mapping(ts_code=ts_code, trade_date=date)
            if not df_map.empty:
                return df_map['mapping_ts_code'].iloc[0]
        except Exception as e:
            logger.exception(f"[find_main_contract] Error fetching mapping: {e}")
        return None
