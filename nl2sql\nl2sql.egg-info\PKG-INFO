Metadata-Version: 2.4
Name: nl2sql
Version: 0.1.0
Summary: HiG<PERSON>lVault MCP Hub's nl2sql module's virtual environment
Requires-Python: >=3.12
Description-Content-Type: text/markdown
Requires-Dist: aiofiles>=24.1.0
Requires-Dist: alembic>=1.16.2
Requires-Dist: asyncmy>=0.2.10
Requires-Dist: cryptography>=45.0.5
Requires-Dist: dashscope>=1.23.5
Requires-Dist: easyquotation>=0.7.7
Requires-Dist: greenlet>=3.2.3
Requires-Dist: httpx>=0.28.1
Requires-Dist: json-repair>=0.47.3
Requires-Dist: langchain-huggingface>=0.3.0
Requires-Dist: mcp[cli]>=1.9.4
Requires-Dist: openai>=1.90.0
Requires-Dist: pymysql>=1.1.1
Requires-Dist: pyprojroot>=0.3.0
Requires-Dist: pyyaml>=6.0.2
Requires-Dist: sentence-transformers>=4.1.0
Requires-Dist: sqlalchemy>=2.0.41
Requires-Dist: sqlglot>=26.30.0
Requires-Dist: sympy>=1.14.0
Requires-Dist: tenacity>=9.1.2
Requires-Dist: tiktoken>=0.9.0
Requires-Dist: torch>=2.7.1
Requires-Dist: torchvision>=0.22.1
Requires-Dist: tqsdk>=3.8.3
Requires-Dist: transformers>=4.52.4
Requires-Dist: tushare>=1.4.21

### 安装使用指南

#### 1. create database
```sql
-- 创建用户
CREATE USER '101'@'%' IDENTIFIED BY '101practice';
GRANT ALL PRIVILEGES ON *.* TO '101'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `101_database`;
USE `101_database`;

--创建表
CREATE TABLE GC_daily_data (
    date DATE NOT NULL COMMENT '日期',
    open DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '开盘价',
    high DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '最高价',
    low DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '最低价',
    close DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '收盘价',
    volume INT NULL DEFAULT NULL COMMENT '成交量',
    open_interest INT NULL DEFAULT NULL COMMENT '持仓量',
    `Change` DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '涨跌',
    `Change_percent` DECIMAL(5, 2) NULL DEFAULT NULL COMMENT '涨跌幅',
    symbol VARCHAR(10) NOT NULL COMMENT '合约代码',
    PRIMARY KEY (date, symbol)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

使用mysqlworkbench导入`datavolume/database/GC_daily_data.csv`中的数据到`GC_daily_data`表中
![alt text](images/image.png)
![alt text](images/image-1.png)
![alt text](images/image-2.png)
![alt text](images/image-3.png)


## 配置database相关的config
参考通用配置文件[datavolume/config/sysmte_config.yaml](/datavolume/config/system_config.yaml)中的database_config部分，在本地配置文件 ***datavolume/config/sysmte_config_your-computer-name.yaml*** 中调整为适配本机的内容.
```
  mysql_config:
    host: "localhost"
    port: 3306
    user: "101"
    password: "123456789"
    database: "101_database"
```
