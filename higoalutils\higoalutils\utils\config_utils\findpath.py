# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""utilities to find path for different conditions"""

import os
import importlib.util
import importlib.resources
from pathlib import Path
import inspect

def get_repo_root(anchor_file=".gitignore") -> Path:
    """
    查找包含 anchor_file的目录,作为项目根目录,默认用含.git的目录
    """
    p = Path(__file__).resolve()
    for parent in [p, *p.parents]:
        if (parent / anchor_file).exists():
            return parent
    raise FileNotFoundError(f"Could not find {anchor_file} in any parent directory of {p}")

def get_resource_dir(package: str, anchor_filename: str = "__init__.py") -> Path:
    """
    找到指定包(package)下anchor文件(anchor_filename)的物理目录。
    适合 production/code-distributed 访问包内资源文件。
    """
    with importlib.resources.path(package, anchor_filename) as anchor_file:
        return anchor_file.parent

def get_package_dir(package: str) -> Path:
    """
    获取本地(非zip/egg/wheel)包的物理目录，仅开发环境下可靠。
    一般不需要使用,留在这里作为get_resource_dir的备选.
    """
    spec = importlib.util.find_spec(package)
    if spec is None or spec.origin is None:
        raise ImportError(f"Can't find package {package}")
    return Path(spec.origin).parent

def get_caller_file_dir(level=1) -> Path:
    """
    返回调用方的.py文件所在目录。
    level=1是直接调用者；level=2是上级调用者。
    """
    frame = inspect.stack()[level]
    caller_path = Path(frame.filename).resolve()
    return caller_path.parent

