# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Logger enums."""

from enum import StrEnum

from higoalutils.config.models.error import LoggerConfigMissingError


class LoggerType(StrEnum):
    """Logger type."""

    FILE = "file"
    """File logger."""
    CONSOLE = "console"
    """Console logger."""
    PROGRESS = "progress"
    """Progress logger."""
    NONE = "none"
    """No logger."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'


class LoggerLevel(StrEnum):
    """Logger level."""

    DEBUG = "DEBUG"
    """Debug level."""
    INFO = "INFO"
    """Info level."""
    WARNING = "WARNING"
    """Warning level."""
    ERROR = "ERROR"
    """Error level."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'
    
    @classmethod
    def from_str(cls, level_str: str) -> "LoggerLevel":
        """Get logger level from string."""
        try:
            return cls[level_str.strip().upper()]
        except Exception as err:
            raise LoggerConfigMissingError(level_str) from err