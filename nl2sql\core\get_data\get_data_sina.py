import asyncio
import pandas as pd
import requests
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from higoalutils.config.load_config import get_config

_executor = ThreadPoolExecutor(max_workers=2)

_cfg = get_config().base_config
_data_dir = Path(_cfg.root_dir) / "database"
_data_dir.mkdir(parents=True, exist_ok=True)
_CSV_PATH = _data_dir / "sina_data_queried.csv"

async def get_data_sina(sql: str, parameters=None) -> str:
    return await asyncio.get_running_loop().run_in_executor(
        _executor, _get_quote_sync, sql, parameters
    )

def _get_quote_sync(sql: str, parameters):
    """
    同步拉取新浪期货快照，保存到 CSV 并返回 Markdown 表格
    """
    klines = fetch_futures_snapshot(sql)
    df = kline_resample(klines, parameters)
    df.to_csv(_CSV_PATH, mode='a')
    return format_df_as_markdown(df)
    
def fetch_futures_snapshot(symbol: list[str]) -> dict:
    """
    调用新浪期货 Level-1 快照接口
    """
    url = 'http://stock2.finance.sina.com.cn/futures/api/json.php/IndexService.getInnerFuturesMiniKLine5m?symbol=' + symbol
    text = requests.get(url).text
    data = json.loads(text)
    df = pd.DataFrame(data, columns=['datetime', 'open', 'high', 'low', 'close', 'volume'])
    df["symbol"] = symbol
    return df    

def kline_resample(df: pd.DataFrame, parameters) -> pd.DataFrame:    
    df = df.copy()
    duration_sec = getattr(parameters, 'duration_sec', 3600)
    df['datetime'] = pd.to_datetime(df['datetime'])
    df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0).astype(int)
    df = df.set_index('datetime').sort_index()
    start_time = "08:55"
    end_time  = "15:05"
    # 2. 计算原始频率
    orig_delta = int(df.index.to_series().diff().dropna().mode()[0].total_seconds())

    # 3. 通过输入输出时间判断模式，no resampling, upsampling, downsampling
    if duration_sec <= orig_delta:
        return df.reset_index()
    # elif duration_sec < orig_delta:
    #     # upsampling：前向填充, 暂时不使用此方法
    #     df_resampled = df.resample(f'{duration_sec}S').ffill().dropna(how='all').reset_index()
    #     fill_missing = getattr(parameters, 'fill_missing', True)
    #     if fill_missing:
    #         df_resampled = fill_missing_candles(df_resampled, duration_sec)
    else:
        # downsampling：C 级 resample + agg
        agg_dict = {
            'open':  'first',
            'high':  'max',
            'low':   'min',
            'close': 'last',
            'volume':'sum'
        }
        df_resampled = df.resample(f'{duration_sec}S').agg(agg_dict).dropna(how="all").reset_index()
        # 过滤掉非交易时间段
        if duration_sec < 86400:
            times = df_resampled['datetime'].dt.time
            t0 = pd.to_datetime(start_time).time()
            t1 = pd.to_datetime(end_time).time()
            df_resampled = df_resampled[(times < t1)&(times >= t0)]
        return df_resampled


def fill_missing_candles(df: pd.DataFrame, duration_sec: int) -> pd.DataFrame:
    """
    对重采样后缺失的周期补全零量平盘 K 线。
    """
    df = df.copy()
    df['datetime'] = pd.to_datetime(df['datetime'])
    df = df.set_index('datetime')

    rule = f'{duration_sec}S'
    full_idx = pd.date_range(df.index[0], df.index[-1], freq=rule)
    df = df.reindex(full_idx)

    missing = df['open'].isna()
    if missing.any():
        prev_close = df['close'].ffill()
        df.loc[missing, ['open','high','low','close']] = \
            prev_close[missing].values[:, None]
        df.loc[missing, 'volume'] = 0
    return df.reset_index().rename(columns={'index':'datetime'})

def format_df_as_markdown(df: pd.DataFrame, max_columns_per_row: int = 15) -> str:
    if df.empty:
        return "*空表格*"

    markdown_lines = []
    total_cols = df.shape[1]
    col_names = list(df.columns)
    row = df.iloc[0]  # 默认仅格式化第一行

    # 分块输出
    for i in range(0, total_cols, max_columns_per_row):
        chunk_cols = col_names[i:i + max_columns_per_row]

        # 表头
        header = "|" + "|".join(chunk_cols) + "|"
        divider = "|" + "|".join(["---"] * len(chunk_cols)) + "|"
        values = "|" + "|".join(str(row[col]) for col in chunk_cols) + "|"

        markdown_lines.extend([header, divider, values, ""])  # 每块空一行分隔

    return "\n".join(markdown_lines)

# import easyquotation
# from higoalutils.logger.log_data import append_pd_to_csv
# def _get_real_sync(sql: str):
#     # 通过easy quotation 拉取股票内容，目前不需要
#     quotation = easyquotation.use('sina')
#     quote_dict = quotation.real(sql, prefix=True)
#     df = pd.DataFrame.from_dict(quote_dict, orient='index')
#     df = append_pd_to_csv(df, _CSV_PATH, 'symbol')
#     return format_df_as_markdown(df)