# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Models for the relational database."""

from pydantic import BaseModel

from higoalutils.database.relational_database.enums import RelationDatabaseType


class RelationDatabaseConfig(BaseModel):
    """Relational database config."""

    type: RelationDatabaseType

class MysqlConfig(BaseModel):
    """Mysql config."""

    user: str
    password: str
    host: str
    port: int
    database: str
    echo: bool = False
    future: bool = True
    pool_size: int = 5
    pool_recycle: int = 3600
    pool_timeout: int = 30
    pool_pre_ping: bool = True
    max_overflow: int = 10
    connect_args: dict = {}

class SqliteConfig(BaseModel):
    """Sqlite config."""

    path: str


class OBReConfig(BaseModel):
    """OceanBase config."""

    user: str
    password: str
    host: str
    port: int
    database: str
