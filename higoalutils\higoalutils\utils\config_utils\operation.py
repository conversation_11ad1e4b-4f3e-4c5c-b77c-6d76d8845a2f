# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Configuration loading and parsing."""

import os
import socket
from pathlib import Path
from string import Template
from typing import Any

import yaml
from dotenv import load_dotenv

from higoalutils.config.defaults import DEFAULT_ENV_FILE


def load_dotenv_file(config_dir: Path) -> None:
    """Load .env and .env_<hostname> file from config_dir if they exist."""
    dotenv_file = config_dir / DEFAULT_ENV_FILE
    if dotenv_file.exists():
        load_dotenv(dotenv_file)
    hostname = socket.gethostname()
    dotenv_file_host = config_dir / f"{DEFAULT_ENV_FILE}_{hostname}"
    if dotenv_file_host.exists():
        load_dotenv(dotenv_file_host, override=True)
    return

def load_yaml_file(file_path: Path) -> dict[str, Any]:
    """Load YAML file config.yaml and config_<hostname>.yaml, parse environment variables."""
    hostname = socket.gethostname()
    file_path_localhost = file_path.with_name(f"{file_path.stem}_{hostname}{file_path.suffix}")
    config_data_all: dict[str, Any] = {}
    for file in [file_path, file_path_localhost]:
        if not file.exists():
            continue
        config_text = file.read_text(encoding="utf-8")
        config_text = parse_env_variables(config_text) # 解析环境变量, 比如API_KEY=${API_KEY}, 会被替换成实际的值
        config_data = yaml.safe_load(config_text)
        config_data_all.update(config_data)
    if not config_data_all:
        msg = f"The configuration file was not found at {file_path}"
        raise FileNotFoundError(msg)
    return config_data_all

def parse_env_variables(text: str) -> str:
    """Replace ${ENV_VAR} in YAML with actual env values."""
    return Template(text).safe_substitute(os.environ)


def apply_overrides(data: dict[str, Any], overrides: dict[str, Any]) -> None:
    """Apply CLI overrides in dot notation like 'a.b.c' = 1."""
    for key, value in overrides.items():
        keys = key.split(".")
        target = data
        current_path_parts = []
        for k in keys[:-1]:
            current_path_parts.append(k)
            target_obj = target.get(k, {})
            if not isinstance(target_obj, dict):
                msg = f"Cannot override non-dict value at {'.'.join(current_path_parts)}"
                raise TypeError(msg)
            target[k] = target_obj
            target = target[k]
        target[keys[-1]] = value