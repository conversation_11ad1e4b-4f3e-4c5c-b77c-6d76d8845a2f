# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Base classes for vector stores."""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import StrEnum
from typing import Any


class FilterColumn(StrEnum):
    """Enum for the column of vector store to filter."""

    DOCID = "source_doc_id"
    """Column name for the source document id."""
    ID = "id"
    """Column name for the chunk id."""


@dataclass
class VectorStoreDocument:
    """Vector store document."""

    id: str
    source_doc_id: str | None
    text: str | None
    vector: list[float] | None
    metadata: dict[str, Any] = field(default_factory=dict)


@dataclass
class VectorStoreSearchResult:
    """Vector store search result."""

    document: VectorStoreDocument
    score: float


class VectorStoreBase(ABC):
    """Vector store base class."""

    @abstractmethod
    def warmup(self) -> None:
        """Warmup the vector store."""
        ...

    @abstractmethod
    def close(self) -> None:
        """Close the vector store."""
        ...

    @abstractmethod
    def load_documents(self, documents: list[VectorStoreDocument], table_name: str, overwrite: bool | None = None) -> None:
        """Load documents into the vector store."""
        ...

    @abstractmethod
    def similarity_search_by_vector(
        self,
        vector: list[float],
        table_name: str,
        top_k: int | None = None,
        similarity_threshold: float | None = None,
        filter_column: str | None = None,
        filter_values: list[str] | None = None
    ) -> list[VectorStoreSearchResult]:
        """Search for documents by vector."""
        ...

    @abstractmethod
    def search_by_id(self, table_name: str, id: str) -> VectorStoreDocument:
        """Search for a document by id."""
        ...

    @abstractmethod
    def get_full_table_name(self, simple_name: str, **kwargs) -> str:
        """Get the full table name."""
        ...