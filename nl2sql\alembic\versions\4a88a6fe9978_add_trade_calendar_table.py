"""add trade calendar table

Revision ID: 4a88a6fe9978
Revises: 0001_initial
Create Date: 2025-07-26 20:10:06.384606

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect
from nl2sql.config.load_nl2sql_config import get_nl2sql_config

# revision identifiers, used by Alembic.
revision = "0002_add_trade_calendar"
down_revision = "0001_initial"
branch_labels = None
depends_on = None

cfg = get_nl2sql_config().ex_config
trade_calendar_csv = f'{cfg.data_csv_path}/trade_calendar.csv'


def upgrade():
    conn = op.get_bind()
    insp = inspect(conn)

    if not insp.has_table("trade_calendar"):
        op.create_table(
            "trade_calendar",
            sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
            sa.Column("exchange", sa.String(10), nullable=False),
            sa.Column("cal_date", sa.Date, nullable=False),
            sa.Column("is_open", sa.<PERSON>, nullable=False),
            sa.Column("pretrade_date", sa.Date, nullable=True),
            mysql_engine="InnoDB",
        )
        op.create_unique_constraint(
            "uq_trade_calendar_exchange_cal_date",
            "trade_calendar",
            ["exchange", "cal_date"],
        )
        # DO NOT add op.create_index on the same columns! It's already covered

    conn.execute(sa.text("""
      LOAD DATA LOCAL INFILE :f
      INTO TABLE trade_calendar
      FIELDS TERMINATED BY ',' IGNORE 1 LINES
      (exchange, @cal_date, @is_open, @pretrade_date)
      SET
        cal_date = STR_TO_DATE(@cal_date, '%Y%m%d'),
        is_open = IF(@is_open IN ('1', 1), 1, 0),
        pretrade_date = IF(@pretrade_date='', NULL, STR_TO_DATE(@pretrade_date, '%Y%m%d'))
    """), {"f": trade_calendar_csv})
    
    
def downgrade():
    op.drop_table("trade_calendar")