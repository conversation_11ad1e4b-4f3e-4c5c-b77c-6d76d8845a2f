# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""SqliteEngine."""

from sqlalchemy import text
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import DeclarativeMeta, declarative_base

from higoalutils.config.load_config import get_config
from higoalutils.config.models.error import DatabaseConfigMissingError
from higoalutils.database.relational_database.base import DatabaseEngineBase
from higoalutils.database.relational_database.enums import RelationDatabaseType


class SqliteEngine(DatabaseEngineBase):
    """SqliteEngine."""

    def __init__(self):
        sqlite_cfg = get_config().database_config.sqlite_config
        if not sqlite_cfg:
            raise DatabaseConfigMissingError(RelationDatabaseType.SQLITE.value)
        uri = f"sqlite+aiosqlite:///{sqlite_cfg.path}"
        self._Base = declarative_base()
        self._engine = create_async_engine(uri, future=True, echo=False)
        self._sessionmaker = async_sessionmaker(bind=self._engine, class_=AsyncSession)

    def get_engine(self) -> AsyncEngine:
        """Get Sqlite engine."""
        return self._engine

    def get_sessionmaker(self) -> async_sessionmaker[AsyncSession]:
        """Get Sqlite sessionmaker."""
        return self._sessionmaker
    
    def get_base(self) -> DeclarativeMeta:
        """Get Sqlite base."""
        return self._Base

    async def warmup(self):
        """Warmup Sqlite engine."""
        async with self._engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

    async def close(self):
        """Close Sqlite engine."""
        await self._engine.dispose()