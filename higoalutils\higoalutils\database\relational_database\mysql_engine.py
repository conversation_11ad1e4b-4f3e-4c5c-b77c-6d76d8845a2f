# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""MySQL database engine implementation."""

from sqlalchemy import text
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import DeclarativeMeta, declarative_base

from higoalutils.config.load_config import get_config
from higoalutils.config.models.error import DatabaseConfigMissingError
from higoalutils.database.relational_database.base import DatabaseEngineBase
from higoalutils.database.relational_database.enums import RelationDatabaseType


class MysqlEngine(DatabaseEngineBase):
    """MySQL database engine implementation."""

    def __init__(self):
        mysql_cfg = get_config().database_config.mysql_config
        if not mysql_cfg:
            raise DatabaseConfigMissingError(RelationDatabaseType.MYSQL.value)
        uri = f"mysql+asyncmy://{mysql_cfg.user}:{mysql_cfg.password}@{mysql_cfg.host}:{mysql_cfg.port}/{mysql_cfg.database}"
        self._Base = declarative_base()
        self._engine = create_async_engine(
            uri,
            echo=mysql_cfg.echo,
            future=mysql_cfg.future,
            pool_size=mysql_cfg.pool_size,
            max_overflow=mysql_cfg.max_overflow,
            pool_timeout=mysql_cfg.pool_timeout,
            pool_recycle=mysql_cfg.pool_recycle,
            pool_pre_ping=mysql_cfg.pool_pre_ping,
            connect_args=mysql_cfg.connect_args
        )
        self._sessionmaker = async_sessionmaker(bind=self._engine, class_=AsyncSession)

    def get_engine(self) -> AsyncEngine:
        """Get mysql engine."""
        return self._engine

    def get_sessionmaker(self) -> async_sessionmaker[AsyncSession]:
        """Get mysql sessionmaker."""
        return self._sessionmaker

    def get_base(self) -> DeclarativeMeta:
        """Get mysql base."""
        return self._Base

    async def warmup(self) -> None:
        """Warmup mysql engine."""
        async with self._engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

    async def close(self) -> None:
        """Close mysql engine."""
        await self._engine.dispose()