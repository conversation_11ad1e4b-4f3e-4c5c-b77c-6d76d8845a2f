- regex_detail: >
    当|今|这(?:个)?|本|上(?:个)?|去|昨|前|大前|下(?:个)?|次|来|明|后|大后
  description: >
    规则描述：中文表达相对时间，日、周、月、季、年通用
  regex_rule_name: ${PATTERN_ZH_OFFSET}
  detail_rules:
    当: 0
    今: 0
    这: 0
    这个: 0
    本: 0
    上: -1
    上个: -1
    去: -1
    昨: -1
    前: -2
    大前: -3
    下: 1
    下个: 1
    次: 1
    来: 1
    明: 1
    后: 2
    大后: 3

- regex_detail: >
    前|上|过去|之前|后|未来|接下来|接着
  description: >
    规则描述：中文表达相对时间段的OFFSET方向，日、周、月、季、年通用
  regex_rule_name: ${PATTERN_ZH_DIRECTION}
  detail_rules:
    前: -1
    上: -1
    过去: -1
    之前: -1
    下: 1
    后: 1
    未来: 1
    接下来: 1
    接着: 1

- regex_detail: >
    \s*[、,，]\s*
  description: >
    规则描述：中文当中的分隔符，用于日期衔接，比如：2018年1月1日、1月2日这种格式
  regex_rule_name: ${PATTERN_SEPARATOR}

- regex_detail: >
    \s*(?:[年\-\/.])\s*
  description: >
    规则描述：中文当中的年份分隔符，用于日期，比如：2018年1月1日、YYYY-MM这种格式
  regex_rule_name: ${PATTERN_CONNECTOR_YEAR}

- regex_detail: >
    \s*(?:[月\-\/.])\s*
  description: >
    规则描述：中文当中的月份分隔符，用于日期，比如：1月1日、MM-DD这种格式
  regex_rule_name: ${PATTERN_CONNECTOR_MONTH}

- regex_detail: >
    \s*(?:[到至-])\s*
  description: >
    规则描述：中文当中的日期与日期之间的分隔符，用于日期段，比如：1月1日至（到）MM-DD这种格式
  regex_rule_name: ${PATTERN_CONNECTOR_RANGE}

- regex_detail: >
    (?:日|天|周|星期|月|季度|[Qq]|年)
  description: >
    规则描述：中文当中的日期单位
  regex_rule_name: ${PATTERN_PERIOD}

- regex_detail: >
    (?:[1-7一二三四五六日天])
  description: >
    规则描述：中文当中的星期几的常见写法
  regex_rule_name: ${PATTERN_WEEKDAY}

- regex_detail: >
    \d{2,4}|[零一二三四五六七八九〇百千万]{2,10}
  description: >
    规则描述：中文当中的年份的常见写法
  regex_rule_name: ${SUB_PATTERN_YEAR}

- regex_detail: >
    \d{1,2}|[零一二三四五六七八九十]{1,2}
  description: >
    规则描述：中文当中的月份和日期的常见写法
  regex_rule_name: ${SUB_PATTERN_MONTH_DAY}

- regex_detail: >
    零一二三四五六七八九〇百千万
  description: >
    规则描述：中文表达当中，通用的日期与数字对应关系
  regex_rule_name: ${CN_NUM_MAP}
  detail_rules:
    零: 0
    一: 1
    二: 2
    两: 2
    三: 3
    四: 4
    五: 5
    六: 6
    七: 7
    八: 8
    九: 9
    十: 10
    百: 100
    〇: 0
    千: 1000
    万: 10000
    日: 7
    天: 7

# 以上部分变量名称请不要更改，代码中预定义了一部分名字
# 以下部分变量，可以根据需要增、减，只要与regex_config_patters.yaml对应起来就可以


- regex_detail: >
    今|本|去|前|大前|明|后|大后
  description: >
    规则描述：中文表达年份（指单个年份），以当前日期为参照的
  regex_rule_name: ${PATTERN_ZH_YEAR_FROM_NOW}
  
- regex_detail: >
    当|这(?:个)?|上(?:个)?|下(?:个)?|次
  description: >
    规则描述：中文表达年份（指单个年份\月度，年月通用），以上下文日期为参照的
  regex_rule_name: ${PATTERN_ZH_YEAR_FROM_CONTEXT}

- regex_detail: >
    前|之前|之后|接着
  description: >
    规则描述：中文表达相对时间段（注意是指一个时间段），年，以上下文日期为参照的
  regex_rule_name: ${PATTERN_ZH_YEAR_KEYWORD_FROM_CONTEXT}

- regex_detail: >
    过去|未来|后|接下来|上|下
  description: >
    规则描述：中文表达相对时间段（注意是指一个时间段），年，以上下文日期为参照的
  regex_rule_name: ${PATTERN_ZH_YEAR_KEYWORD_FROM_NOW}

- regex_detail: >
    本|上(?:个)?|下(?:个)?
  description: >
    规则描述：中文表达月份（指单个月份），以当前日期为参照的
  regex_rule_name: ${PATTERN_ZH_MONTH_FROM_NOW}

- regex_detail: >
    本|上(?:个)?|下(?:个)?|这(?:个)?
  description: >
    规则描述：中文表达月份（指单个月份），以当前日期为参照的
  regex_rule_name: ${PATTERN_ZH_WEEK_FROM_NOW}

- regex_detail: >
    今|本|昨|前|大前|明|后|大后
  description: >
    规则描述：中文表达日期（指单天），以当前日期为参照的
  regex_rule_name: ${PATTERN_ZH_DAY_FROM_NOW}

- regex_detail: >
    当|次
  description: >
    规则描述：中文表达日期（指单天），以上下文日期为参照的
  regex_rule_name: ${PATTERN_ZH_DAY_FROM_CONTEXT}

- regex_detail: >
    [0-9零一二两三四五六七八九十百千万]+
  description: >
    规则描述：中文和数字表达数值
  regex_rule_name: ${PATTERN_ZH_COUNT}