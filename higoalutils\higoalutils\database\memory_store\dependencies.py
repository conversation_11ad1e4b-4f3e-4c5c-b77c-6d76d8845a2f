# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Memory store dependencies."""

import logging

from fastapi import Depends

from higoalutils.database.memory_store.base import MemoryStoreBase
from higoalutils.database.memory_store.factory import MemoryStoreFactory

logger = logging.getLogger(__name__)

def get_store_dep():
    """Get memory store dependency."""
    def _dep() -> MemoryStoreBase:
        return MemoryStoreFactory().get_store()
    return Depends(_dep)

async def get_store_dep_ws() -> MemoryStoreBase:  # noqa: RUF029
    """Get memory store dependency for websocket."""
    try:
        # 尝试使用正常的工厂方法
        return MemoryStoreFactory().get_store()
    except (ConnectionError, TimeoutError, OSError) as e:
        # 如果 Redis 连接失败, 使用 NullMemoryStore 作为后备
        logger.exception("Redis connection failed, using NullMemoryStore as fallback", exc_info=e)
        from higoalutils.database.memory_store.null_store import NullMemoryStore
        return NullMemoryStore()