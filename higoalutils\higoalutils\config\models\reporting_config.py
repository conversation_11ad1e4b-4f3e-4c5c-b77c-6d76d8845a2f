# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Parameterization settings for the default configuration."""

from pathlib import Path

from pydantic import BaseModel, Field, model_validator

from higoalutils.config.enums import ReportingType
from higoalutils.utils.config_utils.find_root_dir import find_root_dir


class ReportingConfig(BaseModel):
    """The default configuration section for Reporting."""

    type: ReportingType = Field(
        description="The reporting type to use.",
        default=ReportingType.file,
    )
    base_dir: str = Field(
        description="The base directory for reporting.",
        default="logs",
    )

    def _validate_base_dir(self) -> None:
        """Validate the base directory."""
        root_working_dir = find_root_dir()
        if self.base_dir.strip() == "":
            self.base_dir = root_working_dir + "/" + "logs"

        if root_working_dir not in self.base_dir:
            self.base_dir = str(Path(f"{root_working_dir}/{self.base_dir}"))
        
        base_dir = Path(self.base_dir).resolve()

        if not base_dir.exists():
            base_dir.mkdir(parents=True, exist_ok=True)
            
        if not base_dir.is_dir():
            msg = f"Invalid root directory: {self.base_dir} is not a directory."
            raise FileNotFoundError(msg)
        self.base_dir = str(base_dir)

    file_encoding: str = Field(
        description="The encoding to use for file reporting.",
        default="utf-8",
    )

    @model_validator(mode="after")
    def _validate_model(self):
        """Validate the model configuration."""
        self._validate_base_dir()
        return self