﻿_api,datetime,ask_price1,ask_volume1,bid_price1,bid_volume1,ask_price2,ask_volume2,bid_price2,bid_volume2,ask_price3,ask_volume3,bid_price3,bid_volume3,ask_price4,ask_volume4,bid_price4,bid_volume4,ask_price5,ask_volume5,bid_price5,bid_volume5,last_price,highest,lowest,open,close,average,volume,amount,open_interest,settlement,upper_limit,lower_limit,pre_open_interest,pre_settlement,pre_close,price_tick,price_decs,volume_multiple,max_limit_order_volume,max_market_order_volume,min_limit_order_volume,min_market_order_volume,open_max_market_order_volume,open_max_limit_order_volume,open_min_market_order_volume,open_min_limit_order_volume,underlying_symbol,strike_price,ins_class,instrument_id,instrument_name,exchange_id,expired,trading_time,expire_datetime,delivery_year,delivery_month,last_exercise_datetime,exercise_year,exercise_month,option_class,exercise_type,product_id,iopv,public_float_share_quantity,stock_dividend_ratio,cash_dividend_ratio,expire_rest_days,categories,position_limit,_path,_listener,_task,margin,commission
<tqsdk.api.TqApi object at 0x00000232518AFF80>,2024-08-15 14:59:59.000001,129800,6,128500,6,,0,127100,6,,0,,0,,0,,0,,0,,0,129200,129200,127000,127000,129200,128189,126,16151760,4314,128180,139900,114470,4284,127190,127000,10.0,0,1.0,500,0,1,0,0,500,0,1,,,FUTURE,SHFE.ni2408,沪镍2408,SHFE,True,"{'day': [['09:00:00', '10:15:00'], ['10:30:00', '11:30:00'], ['13:30:00', '15:00:00']], 'night': [['21:00:00', '25:00:00']]}",1723705200.0,2024,8,,0,0,,,ni,,0,[],[],-316,"[{'id': 'NONFERROUS_METALS', 'name': '有色金属'}]",600,"['quotes', 'SHFE.ni2408']",set(),"<Task finished name='Task-42' coro=<ensure_quote_with_underlying() done, defined at f:\HiGoal_MCP_hub\nl2sql\.venv\Lib\site-packages\tqsdk\objs_not_entity.py:36> result=<tqsdk.objs.Q...ission': 3.0})>",10175.2,3.0
