# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Language model configuration."""

from pydantic import BaseModel, Field

from higoalutils.config.enums import AsyncType, TokenizerType


class LanguageModelConfig(BaseModel):
    """Language model configuration."""

    default_model: str = Field(
        description="The LLM model to use.",
        default="",
    )
    default_encoding_model: TokenizerType = Field(
        description="The encoding model to use",
        default=TokenizerType.DeepSeekTokenizer,
    )
    model_supports_json: bool | None = Field(
        description="Whether the model supports JSON output mode.",
        default=None,
    )
    request_timeout: float = Field(
        description="The request timeout to use.",
        default=180.0,
    )
    tokens_per_minute: int = Field(
        description="The number of tokens per minute to use for the LLM service.",
        default=50_000,
    )
    requests_per_minute: int = Field(
        description="The number of requests per minute to use for the LLM service.",
        default=1_000,
    )
    retry_strategy: str = Field(
        description="The retry strategy to use for the LLM service.",
        default="native",
    )
    max_retries: int = Field(
        description="The maximum number of retries to use for the LLM service.",
        default=10,
    )
    max_retry_wait: float = Field(
        description="The maximum retry wait to use for the LLM service.",
        default=10.0,
    )
    parallelization_num_threads: int = Field(
        description="Number of parallel threads",
        default=1
    )
    parallelization_stagger: float = Field(
        description="Stagger time for parallelization",
        default=0.0
    )
    async_mode: AsyncType = Field(
        description="The async mode to use.",
        default=AsyncType.THREADED
    )
    responses: list[str | BaseModel] | None = Field(
        description="Static responses to use in mock mode.",
        default=None
    )
    max_tokens: int = Field(
        description="The maximum number of tokens to generate.",
        default=8192 # 如果是openai 此处值为 4000
    )
    temperature: float = Field(
        description="The temperature to use for token generation.",
        default=0
    )
    top_p: float = Field(
        description="The top-p value to use for token generation.",
        default=1
    )