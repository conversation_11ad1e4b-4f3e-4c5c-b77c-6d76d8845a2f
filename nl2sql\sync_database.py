from pathlib import Path

import pandas as pd
from higoalutils.config.load_config import get_config
from sqlalchemy import create_engine, text

# 1. DB & Data Config
cfg = get_config().database_config.mysql_config
MYSQL_URL = f"mysql+pymysql://{cfg.user}:{cfg.password}@{cfg.host}:{cfg.port}/{cfg.database}?local_infile=1"
DATA_DIR = Path(get_config().base_config.root_dir) / "database/future"

# 2. Order matters! Truncate children first
TABLES_AND_CSVS = [
    ("window_symbol_map", "window_symbol_map.csv"),
    ("trading_window", "trading_window.csv"),
    ("symbol", "symbol.csv"),
    ("exchange", "exchange.csv"),
    ("trade_calendar", "trade_calendar.csv"),
]

def reload_table(engine, table, csv_filename):
    """Reload Database table from a CSV file."""
    csv_path = DATA_DIR / csv_filename
    if not csv_path.exists():
        print(f"CSV {csv_path} not found, skipping.")
        return
    df = pd.read_csv(csv_path)
    # Optionally, adjust DataFrame columns here to match DB schema
    print(f"Inserting {len(df)} rows into {table}")
    with engine.begin() as conn:
        conn.execute(text(f"TRUNCATE TABLE `{table}`"))
        try:
            df.to_sql(table, conn, if_exists="append", index=False)
        except Exception as e:
            print(f"Error inserting into {table}: {e}")

def main():
    """Main function to sync database with CSV files."""
    engine = create_engine(MYSQL_URL)
    with engine.begin() as conn:
        # Disable foreign key checks
        conn.execute(text("SET FOREIGN_KEY_CHECKS=0"))
        for table, _ in TABLES_AND_CSVS:
            print(f"Truncating {table}")
            conn.execute(text(f"TRUNCATE TABLE `{table}`"))
        conn.execute(text("SET FOREIGN_KEY_CHECKS=1"))
    # Load data
    for table, csv_filename in reversed(TABLES_AND_CSVS):  # Parents last!
        reload_table(engine, table, csv_filename)
    print("Sync complete.")

if __name__ == "__main__":
    main()