# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Null Vector Store."""

from typing import Any

from higoalutils.database.vector_store.base import (
    VectorStoreBase,
    VectorStoreDocument,
    VectorStoreSearchResult,
)


class NullVectorStore(VectorStoreBase):
    """Null Vector Store."""

    def _open_table(self, **kwargs: Any) -> Any:
        return None

    def load_documents(self, documents: list[VectorStoreDocument], table_name: str, overwrite: bool | None = None) -> None:
        """Load documents into the vector store."""
        return

    def close(self) -> None:
        """Close the vector store."""
        return

    def warmup(self) -> None:
        """Warmup the vector store."""
        return

    def _filter_by_id(self, include_ids: list[str] | list[int]) -> Any:
        """Filter by document id."""
        return None

    def _filter_by_doc_id(self, include_doc_ids: list[str] | list[int]) -> Any:
        """Filter by document id."""
        return None

    def similarity_search_by_vector(
        self,
        vector: list[float],
        table_name: str,
        top_k: int | None = None,
        similarity_threshold: float | None = None,
        filter_column: str | None = None,
        filter_values: list[str] | None = None
    ) -> list[VectorStoreSearchResult]:
       """Similarity search by vector."""
       return []

    def search_by_id(self, table_name: str, id: str) -> VectorStoreDocument:
        """Search by id."""
        return VectorStoreDocument(id=id, source_doc_id=None, text=None,  vector=None)

    def get_full_table_name(self, simple_name: str, **kwargs) -> str:
        """Get full table name."""
        return f"null_{simple_name}"