# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Database config."""

from pydantic import BaseModel, model_validator

from higoalutils.config.models.error import DatabaseConfigMissingError
from higoalutils.database.memory_store.enums import MemoryDatabaseType
from higoalutils.database.memory_store.models import MemoryDatabaseConfig, RedisConfig
from higoalutils.database.relational_database.enums import RelationDatabaseType
from higoalutils.database.relational_database.models import (
    MysqlConfig,
    OBReConfig,
    RelationDatabaseConfig,
    SqliteConfig,
)
from higoalutils.database.vector_store.enums import VectorDatabaseType
from higoalutils.database.vector_store.models import (
    LancedbConfig,
    OBVeConfig,
    VectorDatabaseConfig,
)


class DatabaseConfig(BaseModel):
    """Database config."""

    memery_database_config: MemoryDatabaseConfig
    relation_database_config: RelationDatabaseConfig
    vector_database_config: VectorDatabaseConfig

    redis_config: RedisConfig | None = None
    mysql_config: MysqlConfig | None = None
    oceanbase_relational_config: OBReConfig | None = None
    sqlite_config: SqliteConfig | None = None
    lancedb_config: LancedbConfig | None = None
    oceanbase_vector_config: OBVeConfig | None = None

    @model_validator(mode="after")
    def validate_selected_config(self) -> "DatabaseConfig":
        """Check selected config."""
        # 检查 memory_database_config
        if self.memery_database_config:  # noqa: SIM102
            if self.memery_database_config.type == MemoryDatabaseType.REDIS and self.redis_config is None:
                    raise DatabaseConfigMissingError(MemoryDatabaseType.REDIS.value)

        # 检查 relation_database_config
        if self.relation_database_config:
            db_type = self.relation_database_config.type
            if db_type == RelationDatabaseType.MYSQL and self.mysql_config is None:
                    raise DatabaseConfigMissingError(RelationDatabaseType.MYSQL.value)
            if db_type == RelationDatabaseType.SQLITE and self.sqlite_config is None:
                    raise DatabaseConfigMissingError(RelationDatabaseType.SQLITE.value)
            if db_type == RelationDatabaseType.OCEANBASE and self.oceanbase_relational_config is None:
                    raise DatabaseConfigMissingError(RelationDatabaseType.OCEANBASE.value)

        # 检查 vector_database_config
        if self.vector_database_config:
            vec_type = self.vector_database_config.type
            if vec_type == VectorDatabaseType.LANCEDB and self.lancedb_config is None:
                    raise DatabaseConfigMissingError(VectorDatabaseType.LANCEDB.value)
            if vec_type == VectorDatabaseType.OCEANBASE and self.oceanbase_vector_config is None:
                    raise DatabaseConfigMissingError(VectorDatabaseType.OCEANBASE.value)

        return self