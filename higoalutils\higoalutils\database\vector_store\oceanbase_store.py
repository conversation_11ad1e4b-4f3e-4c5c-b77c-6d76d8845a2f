# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""OceanBase Vector Store."""

import json
import logging
import time

from pyobvector import VECTOR, ObVecClient
from sqlalchemy import JSON as SQL_JSON
from sqlalchemy import Column, String, Text, func

from higoalutils.config.load_config import get_config
from higoalutils.config.models.error import DatabaseConfigMissingError
from higoalutils.database.vector_store.base import (
    VectorStoreBase,
    VectorStoreDocument,
    VectorStoreSearchResult,
)
from higoalutils.database.vector_store.enums import VectorDatabaseType
from higoalutils.database.vector_store.utils import normalize_vector

logger = logging.getLogger(__name__)

class OBVectorStore(VectorStoreBase):
    """OceanBase vector store."""

    def __init__(self):
        database_config = get_config().database_config
        ob_cfg = get_config().database_config.oceanbase_vector_config
        vector_config = database_config.vector_database_config
        if not ob_cfg:
            raise DatabaseConfigMissingError(VectorDatabaseType.OCEANBASE.value)
        self.container = vector_config.container_name
        self.top_k = vector_config.default_top_k
        self.similarity_threshold = vector_config.default_similarity_threshold
        self.overwrite = vector_config.overwrite
        self.database = ob_cfg.database

        self.client = ObVecClient(
            uri=f"{ob_cfg.host}:{ob_cfg.port}",
            user=ob_cfg.user,
            password=ob_cfg.password,
            db_name=ob_cfg.database
        )

    def warmup(self) -> None:
        """Warmup the database engine."""
        self.client.perform_raw_text_sql("SHOW TABLES")

    def close(self) -> None:
        """Close the database engine."""
        return  # ObVecClient 无需关闭

    def get_full_table_name(self, simple_name: str, **kwargs) -> str:
        """Get the full table name with optional suffixes from kwargs."""
        base_name = f"{self.container}_{simple_name}"
        suffix = "_".join(str(value) for value in kwargs.values())
        return f"{base_name}_{suffix}" if suffix else base_name

    def _get_columns(self) -> list[Column]:
        return [
            Column("id", String(64), primary_key=True),
            Column("source_doc_id", String(64)),
            Column("text", Text),
            Column("vector", VECTOR(1024)),
            Column("metadata", SQL_JSON),
        ]

    def load_documents(self, documents: list[VectorStoreDocument], table_name: str, overwrite: bool | None = None) -> None:
        """Load documents into the vector store."""
        overwrite = overwrite if overwrite is not None else self.overwrite

        self.client.perform_raw_text_sql(f"USE {self.database}")

        if overwrite:
            self.client.perform_raw_text_sql(f"DROP TABLE IF EXISTS `{table_name}`")

        # 创建表结构
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS `{table_name}` (
            id VARCHAR(255) PRIMARY KEY,
            source_doc_id VARCHAR(255),
            text TEXT,
            vector VECTOR(1024),
            metadata JSON,
            VECTOR INDEX `{table_name}_vector_index` (vector) WITH (distance=cosine, type=hnsw)
        )
        """
        self.client.perform_raw_text_sql(create_table_sql)

        data = []
        for doc in documents:
            if doc.vector is not None:
                normalized_vector = normalize_vector(doc.vector)
                data.append({
                    "id": doc.id,
                    "source_doc_id": doc.source_doc_id,
                    "text": doc.text,
                    "vector": normalized_vector,
                    "metadata": doc.metadata,
                })

        if data:
            self.client.insert(table_name, data=data)

    def _build_filter(self, column: str | None, values: list[str] | None) -> str | None:
        """构造 SQL 的 WHERE 筛选子句."""
        if not column or not values:
            return None
        quoted = ", ".join(f"'{v}'" for v in values)
        return f"{column} IN ({quoted})"
    
    def similarity_search_by_vector(
        self,
        vector: list[float],
        table_name: str,
        top_k: int | None = None,
        similarity_threshold: float | None = None,
        filter_column: str | None = None,
        filter_values: list[str] | None = None
    ) -> list[VectorStoreSearchResult]:
        """Search by vector."""
        t1 = time.time()
        top_k = top_k if top_k is not None else self.top_k
        sim_t = similarity_threshold if similarity_threshold is not None else self.similarity_threshold
        query_filter = self._build_filter(filter_column, filter_values)
        
        res = self.client.ann_search(
            table_name=table_name,
            vec_data=normalize_vector(vector),
            vec_column_name="vector",
            distance_func=func.cosine_distance,
            topk=top_k * 3,
            with_dist=True,
            output_column_names=["id", "source_doc_id", "text", "vector", "metadata"],
            metric="cosine",
            where_clause=query_filter
        )
        results = []
        for _i, row in enumerate(res):
            *fields, distance = row
            score = 1 - float(distance)

            if sim_t > 0 and score < sim_t:
                continue
            
            id_, source_doc_id, text, vector_data, metadata_json = fields
            metadata = metadata_json if isinstance(metadata_json, dict) else json.loads(metadata_json)

            results.append(VectorStoreSearchResult(
                document=VectorStoreDocument(
                    id=id_,
                    source_doc_id=source_doc_id,
                    text=text,
                    vector=vector_data,
                    metadata=metadata,
                ),
                score=score
            ))
        t2 = time.time()
        logger.info("查询 LanceDB 向量库 共 %d 个结果通过相似度过滤, 返回 Top %d, 耗时 %.2f 秒", len(results), top_k, t2 - t1)
        return results[:top_k]

    def search_by_id(self, table_name: str, id: str) -> VectorStoreDocument:
        """Search by id."""
        query = self.client.select(  # type: ignore
            table_name=table_name,
            where=f"id = '{id}'",
            output_column_names=["id", "source_doc_id", "text", "vector", "metadata"]
        )
        if not query:
            return VectorStoreDocument(id=id, source_doc_id=None, text=None, vector=None)
        doc = query[0]
        return VectorStoreDocument(
            id=doc["id"],
            source_doc_id=doc.get("source_doc_id"),
            text=doc.get("text"),
            vector=doc.get("vector"),
            metadata=doc.get("metadata", {}),
        )