# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Singleton manager for database engine instances."""

from typing import TYPE_CHECKING

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeMeta

from higoalutils.database.relational_database.factory import create_engine_instance
from higoalutils.utils.singleton_utils.singleton import singleton

if TYPE_CHECKING:
    from higoalutils.database.relational_database.base import DatabaseEngineBase


@singleton
class DBEngineManager:
    """Singleton manager for database engine instances."""

    def __init__(self):
        self._impl: DatabaseEngineBase = create_engine_instance()

    def get_engine(self) -> AsyncEngine:
        """Get the database engine instance."""
        return self._impl.get_engine()

    def get_sessionmaker(self) -> async_sessionmaker[AsyncSession]:
        """Get the sessionmaker instance."""
        return self._impl.get_sessionmaker()
    
    def get_base(self) -> DeclarativeMeta:
        """Get the base instance."""
        return self._impl.get_base()

    async def warmup(self) -> None:
        """Warmup the database engine."""
        await self._impl.warmup()

    async def close(self) -> None:
        """Close the database engine."""
        await self._impl.close()