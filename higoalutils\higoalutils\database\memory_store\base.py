# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Memory store base class."""

from abc import ABC, abstractmethod
from collections.abc import AsyncGenerator, AsyncIterator
from typing import Any


class MemoryStoreBase(ABC):
    """Memory store base class."""
    
    @abstractmethod
    async def get(self, key: str) -> Any:
        """Get a value from the store."""
        ...

    @abstractmethod
    async def set(self, key: str, value: Any, expire: int | None = None) -> None:
        """Set a value in the store."""
        ...

    @abstractmethod
    async def has(self, key: str) -> bool:
        """Check if a key exists in the store."""
        ...

    @abstractmethod
    async def delete(self, *keys: str) -> None:
        """Delete one or more keys from the store."""
        ...

    @abstractmethod
    async def clear(self) -> None:
        """Clear all keys from the store."""
        ...

    @abstractmethod
    async def close(self) -> None:
        """Close the store."""
        ...

    @abstractmethod
    async def warmup(self) -> None:
        """Warmup the store."""
        ...
    
    @abstractmethod
    def subscribe(self, channel: str) -> AsyncGenerator[str, None]:
        """Subscribe to a channel."""
        ...
    
    @abstractmethod
    async def publish(self, channel: str, message: str) -> None:
        """Publish a message to a channel."""
        ...
    
    @abstractmethod
    def scan_iter(self, match: str) -> AsyncIterator[str]:
        """Scan the store for keys matching a pattern."""
        ...