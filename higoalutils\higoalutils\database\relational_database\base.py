# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Base classes for relational database engines."""

from abc import ABC, abstractmethod

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeMeta


class DatabaseEngineBase(ABC):
    """Base class for relational database engines."""

    @abstractmethod
    def get_engine(self) -> AsyncEngine:
        """Get the SQLAlchemy engine."""
        ...
    
    @abstractmethod
    def get_sessionmaker(self) -> async_sessionmaker[AsyncSession]:
        """Get the SQLAlchemy sessionmaker."""
        ...
    
    @abstractmethod
    def get_base(self) -> DeclarativeMeta:
        """Get the SQLAlchemy base."""
        ...
    
    @abstractmethod
    async def warmup(self) -> None:
        """Warmup the database engine."""
        ...
    
    @abstractmethod
    async def close(self) -> None:
        """Close the database engine."""
        ...