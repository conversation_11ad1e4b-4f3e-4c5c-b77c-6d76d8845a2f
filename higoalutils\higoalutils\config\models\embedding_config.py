# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Embedding configuration models."""

from pydantic import BaseModel, Field, model_validator

from higoalutils.config.enums import AsyncType, DeviceType, EmbeddingType
from higoalutils.config.models.error import EmbeddingConfigMissingError


class EmbeddingModelConfig(BaseModel):
    """Embedding model configuration model."""

    type: EmbeddingType

class HuggingFaceEmbeddingConfig(BaseModel):
    """HuggingFace embedding model configuration model."""

    default_model: str = Field(
        default="BAAI/bge-large-zh-v1.5"
    )
    device: DeviceType = Field(
        default=DeviceType.CPU
    )
    parallelization_num_threads: int = Field(
        default=50
    )
    parallelization_stagger: float = Field(
        default=0.3
    )
    async_mode: AsyncType = Field(
        default=AsyncType.THREADED
    )


class OpenAIEmbeddingConfig(BaseModel):
    """OpenAI embedding model configuration model."""
    
    default_model: str = Field(
        default="text-embedding-v3"
    )
    request_timeout: float = Field(
        default=30.0
    )
    retry_strategy: str = Field(
        default="exponential"
    )
    max_retries: int = Field(
        default=3
    )
    max_retry_wait: float = Field(
        default=10.0
    )
    async_mode: AsyncType = Field(
        default=AsyncType.THREADED
    )


class EmbeddingConfig(BaseModel):
    """Embedding configuration model."""

    embedding_model_config: EmbeddingModelConfig
    huggingface_embedding_config: HuggingFaceEmbeddingConfig | None = None
    openai_embedding_config: OpenAIEmbeddingConfig | None = None

    @model_validator(mode="after")
    def validate_active_model(self):
        """Validate that the active model is set."""
        model_type = self.embedding_model_config.type

        if model_type == EmbeddingType.HuggingFace:
            if self.huggingface_embedding_config is None:
                raise EmbeddingConfigMissingError(EmbeddingType.HuggingFace)
        elif model_type == EmbeddingType.OpenAI:
            if self.openai_embedding_config is None:
                raise EmbeddingConfigMissingError(EmbeddingType.OpenAI)
        else:
            raise EmbeddingConfigMissingError(model_type)

        return self