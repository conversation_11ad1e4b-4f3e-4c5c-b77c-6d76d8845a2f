import asyncio
import pandas as pd
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from tqsdk import Tq<PERSON><PERSON>, TqAuth
from nl2sql.core.utils.log_data import log_dict_to_csv, load_spec_file
from higoalutils.config.load_config import get_config
from nl2sql.config.load_nl2sql_config import get_nl2sql_config

_executor = ThreadPoolExecutor(max_workers=2)

_cfg = get_config().base_config
_cfg_tq = get_nl2sql_config().tqsdk_config
_data_dir = Path(_cfg.root_dir) / "database"
_data_dir.mkdir(parents=True, exist_ok=True)
_CSV_PATH = _data_dir / "tq_data_test.csv"
_CSV_PATH_quote = _data_dir / "tq_data_quote.csv"
_CSV_PATH_klines = _data_dir / "tq_data_klines.csv"
_SPEC = load_spec_file(_data_dir/"tq_data_table_naming.csv")

async def get_data_tq(sql: str, parameters):
    usercase = parameters.get("usercase", "quote")
    if usercase == "quote":
        return await asyncio.get_running_loop().run_in_executor(
            _executor, _get_quote_sync, sql
        )
    elif usercase == "kline":
        return await asyncio.get_running_loop().run_in_executor(
            _executor, _get_kline_sync, sql, parameters
        )
        
def _get_quote_sync(sql: str):
    # 确保这段运行在独立线程
    with TqApi(auth=TqAuth(_cfg_tq.user, _cfg_tq.password)) as api:
        quote = api.get_quote(sql)
        quote_dict = vars(quote)
        df = pd.DataFrame([quote_dict])
        # save data locally, TODO: better way to save data, link to SQL?
        log_dict_to_csv(quote_dict, _CSV_PATH, _SPEC)
        df.to_csv(_CSV_PATH_quote, mode='a')
        return format_df_as_markdown(df)

def _get_kline_sync(sql: str, parameters):
    duration_sec = getattr(parameters, 'duration_sec', 60)
    with TqApi(auth=TqAuth(_cfg_tq.user, _cfg_tq.password)) as api:
        #ticks = api.get_tick_serial(sql)
        klines = api.get_kline_serial(sql, duration_seconds=duration_sec)
        klines.to_csv(_CSV_PATH_klines, mode='a')
        return format_df_as_markdown(klines)    
    
def format_df_as_markdown(df: pd.DataFrame, max_columns_per_row: int = 15) -> str:
    if df.empty:
        return "*空表格*"

    markdown_lines = []
    total_cols = df.shape[1]
    col_names = list(df.columns)
    row = df.iloc[0]  # 默认仅格式化第一行

    # 分块输出
    for i in range(0, total_cols, max_columns_per_row):
        chunk_cols = col_names[i:i + max_columns_per_row]

        # 表头
        header = "|" + "|".join(chunk_cols) + "|"
        divider = "|" + "|".join(["---"] * len(chunk_cols)) + "|"
        values = "|" + "|".join(str(row[col]) for col in chunk_cols) + "|"

        markdown_lines.extend([header, divider, values, ""])  # 每块空一行分隔

    return "\n".join(markdown_lines)