# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""工具配置管理模块."""

from higoalutils.config.models.system_config import SystemConfig
from higoalutils.utils.config_utils.factory import config_factory

UtilsConfigManager = config_factory(config_class=SystemConfig)

def get_config() -> SystemConfig:
    """获取配置实例."""
    return UtilsConfigManager.get_config()