base_config:
    root_dir: datavolume
    config_dir: config
    log_dir: logs
    file_encoding: utf-8
    time_zone: Asia/Shanghai

database_config:
  memery_database_config:
    type: "redis" # Optional values: "redis"
  
  relation_database_config:
    type: "mysql" # Optional values: "sqlite", "mysql", "oceanbase", "none"
  
  vector_database_config: 
    type: "lancedb" # Optional values: "lancedb", "oceanbase", "none"
    overwrite: true
    container_name: "defult"
    default_top_k: 3
    default_similarity_threshold: 0.5

  redis_config:
    host: "localhost"
    port: 6379
    password: ""
    db: 0
    max_connections: 10
    pool_recycle: 3600
  
  mysql_config:
    host: "localhost"
    port: 3306
    user: "101"
    password: "${MYSQL_PASSWORD}"
    database: "101_database"

  sqlite_config:
    path: "appdata/database/sqlite/example_sqlite.db"
  
  oceanbase_relational_config:
    user: ""
    password: ""
    host: ""
    port: 0
    database: ""
  
  lancedb_config:
    path: "appdata/database/lancedb"
  
  oceanbase_vector_config:
    host: ""
    port: 0
    user: ""
    password: ""
    database: ""


embedding_config:
  embedding_model_config:
    type: "huggingface_embedding" # Optional values: "openai_embedding", "huggingface_embedding"
  
  huggingface_embedding_config:
    default_model: "BAAI/bge-large-zh-v1.5"
    device: "cpu" # Optional values: "cpu", "cuda", "mps"
    parallelization_num_threads: 50
    parallelization_stagger: 0.3
    async_mode: "threaded"
  
  openai_embedding_config:
    default_model: "text-embedding-v3"
    async_mode: "threaded"

language_model_config:
  default_model: "deepseek-chat"
  default_encoding_model: "deepseek_tokenizer"
  model_supports_json: true
  parallelization_num_threads: 50
  parallelization_stagger: 0.3
  temperature: 0.3
  async_mode: "threaded"

reporting_config:
  type: file
  base_dir: "datavolume/logs"
  file_encoding: utf-8

config_file_for_model: "model_config.yaml"