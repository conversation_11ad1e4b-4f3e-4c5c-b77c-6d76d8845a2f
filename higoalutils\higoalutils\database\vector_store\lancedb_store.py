# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""LanceDB Vector Store."""

import json
import logging
import time
from typing import Any

import lancedb
import pyarrow as pa

from higoalutils.config.load_config import get_config
from higoalutils.config.models.error import DatabaseConfigMissingError
from higoalutils.database.vector_store.base import (
    VectorStoreBase,
    VectorStoreDocument,
    VectorStoreSearchResult,
)
from higoalutils.database.vector_store.enums import VectorDatabaseType
from higoalutils.database.vector_store.utils import normalize_vector

logger = logging.getLogger(__name__)

class LanceDBStore(VectorStoreBase):
    """LanceDB Vector Store."""

    def __init__(self):
        database_config = get_config().database_config
        lancedb_cfg = database_config.lancedb_config
        vector_config = database_config.vector_database_config
        if not lancedb_cfg:
            raise DatabaseConfigMissingError(VectorDatabaseType.LANCEDB.value)
        self.container = vector_config.container_name
        self.top_k = vector_config.default_top_k
        self.similarity_threshold = vector_config.default_similarity_threshold
        self.overwrite = vector_config.overwrite
        self.db_connection = lancedb.connect(lancedb_cfg.path)

    def warmup(self) -> None:
        """Warmup the database engine."""
        self.db_connection.table_names()

    def close(self) -> None:
        """Close the database engine."""
        return

    def _open_table(self, table_name: str) -> Any:
        if table_name in self.db_connection.table_names():
            return self.db_connection.open_table(table_name)
        return None

    def get_full_table_name(self, simple_name: str, **kwargs) -> str:
        """Get the full table name with optional suffixes from kwargs."""
        base_name = f"{self.container}_{simple_name}"
        suffix = "_".join(str(value) for value in kwargs.values())
        return f"{base_name}_{suffix}" if suffix else base_name

    def load_documents(self, documents: list[VectorStoreDocument], table_name: str, overwrite: bool | None = None) -> None:
        """Load documents into the vector store."""
        overwrite = overwrite if overwrite is not None else self.overwrite
        data = [
            {
                "id": doc.id,
                "source_doc_id": doc.source_doc_id,
                "text": doc.text,
                "vector": normalize_vector(doc.vector),
                "metadata": json.dumps(doc.metadata),
            }
            for doc in documents if doc.vector is not None
        ]

        schema = pa.schema([
            pa.field("id", pa.string()),
            pa.field("source_doc_id", pa.string()),
            pa.field("text", pa.string()),
            pa.field("vector", pa.list_(pa.float32())),
            pa.field("metadata", pa.string()),
        ])

        if overwrite:
            if data:
                self.db_connection.create_table(
                    table_name,
                    data=data,
                    mode="overwrite"
                )
            else:
                self.db_connection.create_table(
                    table_name,
                    schema=schema,
                    mode="overwrite"
                )
        else:
            table = self._open_table(table_name)
            if data:
                table.add(data, mode="append")

    def _build_filter(self, column: str | None, values: list[str] | None) -> str | None:
        """构造 SQL 的 WHERE 筛选子句."""
        if not column or not values:
            return None
        quoted = ", ".join(f"'{v}'" for v in values)
        return f"{column} IN ({quoted})"

    def similarity_search_by_vector(
        self,
        vector: list[float],
        table_name: str,
        top_k: int | None = None,
        similarity_threshold: float | None = None,
        filter_column: str | None = None,
        filter_values: list[str] | None = None
    ) -> list[VectorStoreSearchResult]:
        """Search by vector."""
        t1 = time.time()
        top_k = top_k if top_k else self.top_k
        sim_t = similarity_threshold if similarity_threshold else self.similarity_threshold

        table = self._open_table(table_name)
        query_vector = normalize_vector(vector)
        query_filter = self._build_filter(filter_column, filter_values)

        # 明确使用余弦相似度搜索
        docs = table.search(
            query_vector,
            vector_column_name="vector"
        ).where(query_filter, prefilter=True).limit(top_k * 3).to_list() # .metric("cosine")

        results = []
        for _i, doc in enumerate(docs):
            raw_score = float(doc["_distance"])
            normalized_score = 1 - 0.5 * raw_score
            if sim_t > 0 and normalized_score < sim_t:
                continue
            results.append(VectorStoreSearchResult(
                document=VectorStoreDocument(
                    id=doc["id"],
                    text=doc["text"],
                    source_doc_id=doc["source_doc_id"],
                    vector=doc["vector"],
                    metadata=json.loads(doc["metadata"]),
                ),
                score=normalized_score,  # 使用归一化后的分数
            ))
        t2 = time.time()
        logger.info("查询 LanceDB 向量库 共 %d 个结果通过相似度过滤, 返回 Top %d, 耗时 %.2f 秒", len(results), top_k, t2 - t1)
        return results[:top_k]

    def search_by_id(self, table_name: str, id: str) -> VectorStoreDocument:
        """Search by id."""
        table = self._open_table(table_name)
        docs = table.search().where(f"id == '{id}'", prefilter=True).to_list()
        if not docs:
            return VectorStoreDocument(id=id, source_doc_id=None, text=None, vector=None)
        doc = docs[0]
        return VectorStoreDocument(
            id=doc["id"],
            text=doc["text"],
            source_doc_id=doc["source_doc_id"],
            vector=doc["vector"],
            metadata=json.loads(doc["metadata"]),
        )
    