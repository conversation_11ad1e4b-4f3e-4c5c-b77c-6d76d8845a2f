# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Configuration enums."""

from enum import StrEnum


class PlatformType(StrEnum):
    """Platform type."""

    openai = "openai"
    """OpenAI"""
    huggingface = "huggingface"
    """HuggingFace"""
    deepseek = "deepseek"
    """DeepSeek"""
    dashscope = "dashscope"
    """DashScope"""
    nltk = "nltk"
    """NLTK"""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'

class ObjectType(StrEnum):
    """Object type."""

    CHAT = "chat"
    """Chat"""
    EMBEDDING = "embedding"
    """Embedding"""
    SENTENCE_SPLITTER = "sentence_splitter"
    """Sentence Splitter"""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'

class TokenizerType(StrEnum):
    """Tokenizer type."""

    OpenAITokenizer = "cl100k_base"
    """OpenAI tokenizer"""
    DeepSeekTokenizer = "deepseek_tokenizer"
    """DeepSeek tokenizer"""
    DashScopeTokenizer = "dashscope_tokenizer"
    """DashScope tokenizer"""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'


class AsyncType(StrEnum):
    """Enum for the type of async to use."""

    ASYNCIO = "asyncio"
    THREADED = "threaded"


class DeviceType(StrEnum):
    """The device type for the pipeline."""

    CPU = "cpu"
    """The CPU device type."""
    CUDA = "cuda"
    """The CUDA device type."""
    MPS = "mps"
    """The MPS device type."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'


class LanguageCode(StrEnum):
    """Language class definition."""

    Chinese = "zh"
    English = "en"

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'


class EmbeddingType(StrEnum):
    """Embedding type class definition."""

    OpenAI = "openai_embedding"
    HuggingFace = "huggingface_embedding"

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'


class TimeGranularity(StrEnum):
    """The time granularity for the pipeline."""

    YEAR = "year"
    """The year granularity."""
    MONTH = "month"
    """The month granularity."""
    DAY = "day"
    """The day granularity."""
    HOUR = "hour"
    """The hour granularity."""
    MINUTE = "minute"
    """The minute granularity."""
    SECOND = "second"
    """The second granularity."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'




class DatetimeFormat(StrEnum):
    """The datetime format for the pipeline."""

    date = "date"
    """The date format."""
    datetime = "datetime"
    """The datetime format."""
    month = "month"
    """The month format."""
    year = "year"
    """The year format."""
    timezone = "timezone"
    """The timezone format."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'


class TimeType(StrEnum):
    """The time type for the pipeline."""

    READABLE = "readable"
    """The readable time type."""
    UTC = "utc"
    """The UTC time type."""
    TIMESTAMP = "timestamp"
    """The timestamp time type."""
    NATURAL = "natural"
    """The natural time type."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'

class ReportingType(StrEnum):
    """The reporting configuration type for the pipeline."""

    file = "file"
    """The file reporting configuration type."""
    console = "console"
    """The console reporting configuration type."""

    def __repr__(self):
        """Get a string representation."""
        return f'"{self.value}"'