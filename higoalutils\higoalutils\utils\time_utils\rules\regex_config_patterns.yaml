- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
    (?:${PATTERN_CONNECTOR_RANGE})
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
  description: |
    规则描述：日期段识别规则，包括 yyy年mm月dd日到YYYY.MM.DD格式适配
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: day
    match_group:
      fromYear: 1
      fromMonth: 2
      fromDay: 3
      toYear: 4
      toMonth: 5
      toDay: 6

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
    (?:${PATTERN_CONNECTOR_RANGE})
    (${PATTERN_ZH_YEAR_FROM_CONTEXT})${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
  description: |
    规则描述：日期段识别规则，包括 yyy年mm月dd日到次年X月X日格式适配。
    注意，这里的中文语境上非常特殊，第二个次年是依赖第一个年份的，而不是我们讲的明年的概念
    所以，这里引进了一个特殊处理程序，_handle_range_previous_condition专门解决这种前置依赖的
  call_function: _handle_range_previous_condition
  parameters:
    type: range
    offset: context
    time_unit: day
    match_group:
      fromYear: 1
      fromMonth: 2
      fromDay: 3
      toYear: 4
      toMonth: 5
      toDay: 6

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
    (?:${PATTERN_CONNECTOR_RANGE})
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
  description: |
    规则描述：日期段识别规则，包括 yyy年mm月dd日到MM.DD格式适配
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: day
    match_group:
      fromYear: 1
      fromMonth: 2
      fromDay: 3
      toYear: 1
      toMonth: 4
      toDay: 5

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
    (?:${PATTERN_CONNECTOR_RANGE})
    (${SUB_PATTERN_MONTH_DAY})日
  description: |
    规则描述：日期段识别规则，包括 yyy年mm月dd日到dd日格式适配
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: day
    match_group:
      fromYear: 1
      fromMonth: 2
      fromDay: 3
      toYear: 1
      toMonth: 2
      toDay: 4

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})月?
    (?:${PATTERN_CONNECTOR_RANGE})
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})月?
  description: |
    规则描述：月度区间识别规则，包括 yyyy年mm月至YYYY年MM月格式适配
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: month
    match_group:
      fromYear: 1
      fromMonth: 2
      toYear: 3
      toMonth: 4

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})月?
    (?:${PATTERN_CONNECTOR_RANGE})
    (${SUB_PATTERN_MONTH_DAY})月
  description: |
    规则描述：月度区间识别规则，包括 yyyy年mm月至MM月格式适配
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: month
    match_group:
      fromYear: 1
      fromMonth: 2
      toYear: 1
      toMonth: 3

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
  description: |
    规则描述：日期识别规则，包括 yyy年mm月dd日、YYYY-MM-DD、YYYY.MM.DD格式适配
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: now
    time_unit: day
    match_group:
      year: 1
      month: 2
      day: 3

- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_CONTEXT})${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})${PATTERN_CONNECTOR_MONTH}
    (${SUB_PATTERN_MONTH_DAY})日?
  description: |
    规则描述：日期识别规则，包括 上年mm月dd日格式适配
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: context
    time_unit: day
    match_group:
      year: 1
      month: 2
      day: 3

- regex_detail: |
    ((?:${SUB_PATTERN_MONTH_DAY})|(?:${PATTERN_ZH_MONTH_FROM_NOW}))\s*[月\-\/]\s*
    (${SUB_PATTERN_MONTH_DAY})[日|号]?
    (?:${PATTERN_CONNECTOR_RANGE})
    ((?:${SUB_PATTERN_MONTH_DAY})|(?:${PATTERN_ZH_MONTH_FROM_NOW}))\s*[月\-\/]\s*
    (${SUB_PATTERN_MONTH_DAY})[日|号]?
  description: |
    规则描述：日期识别规则，包括 MM月DD日到上月DD日格式适配
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: day
    match_group:
      fromMonth: 1
      fromDay: 2
      toMonth: 3
      toDay: 4

- regex_detail: |
    ((?:${SUB_PATTERN_MONTH_DAY})|(?:${PATTERN_ZH_MONTH_FROM_NOW}))\s*[月\-\/]\s*
    (${SUB_PATTERN_MONTH_DAY})[日|号]?
    (?:${PATTERN_CONNECTOR_RANGE})
    (${SUB_PATTERN_MONTH_DAY})[日|号]?
  description: |
    规则描述：日期识别规则，包括 MM月DD日到DD日格式适配
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: day
    match_group:
      fromMonth: 1
      fromDay: 2
      toMonth: 1
      toDay: 3

- regex_detail: |
    ((?:${PATTERN_SEPARATOR}(?:${SUB_PATTERN_MONTH_DAY})日?)+
    ${PATTERN_SEPARATOR}
    (?:${SUB_PATTERN_MONTH_DAY})日)
  description: |
    规则描述：日期段识别规则，包括 yyy年mm月dd日、dd日格式适配
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: context
    time_unit: day
    match_group:
      multiDay: 1

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (?:最)?(前|后)
    ([1-9一二三四五六七八九十])(?:个)?月
  description: |
    规则描述：中文的特殊语境规则，包括 XX年前3个月，指的是1-3月，格式适配
    此处用到了一个特殊处理的函数 _handle_special_month 专用解决这个语境
  call_function: _handle_special_month
  parameters:
    type:  range
    offset: now
    time_unit: month
    match_group:
      year: 1
      direction: 2
      month: 3


- regex_detail: |
    (${PATTERN_ZH_DAY_FROM_NOW})[日|天]
  description: |
    规则描述：日期识别规则，包括昨天、前天格式适配
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: now
    time_unit: day
    match_group:
      day: 1

- regex_detail: |
    (${PATTERN_ZH_DAY_FROM_CONTEXT})[日|天]
  description: |
    规则描述：日期识别规则，包括次日、当日格式适配
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: context
    time_unit: day
    match_group:
      day: 1


- regex_detail: |
    (${SUB_PATTERN_YEAR})\s*(?:[\-\/.])\s*
    (${SUB_PATTERN_MONTH_DAY})
  description: |
    规则描述：月度识别规则，包括 YYYY-MM、YYYY.MM格式适配
  call_function: _handle_absolute_month
  parameters:
    type: absolute
    offset: now
    time_unit: month
    match_group:
      year: 1
      month: 2

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})月
  description: |
    规则描述：月度识别规则，包括 yyy年mm月、YYYY-MM、YYYY.MM格式适配
  call_function: _handle_absolute_month
  parameters:
    type: absolute
    offset: now
    time_unit: month
    match_group:
      year: 1
      month: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_CONTEXT})${PATTERN_CONNECTOR_YEAR}
    (${SUB_PATTERN_MONTH_DAY})月
  description: |
    规则描述：月度识别规则，包括 上年5月、次年6月, 需要依上文取日期的格式适配
  call_function: _handle_absolute_month
  parameters:
    type: absolute
    offset: context
    time_unit: month
    match_group:
      year: 1
      month: 2

- regex_detail: |
    ((?:${SUB_PATTERN_MONTH_DAY})|(?:${PATTERN_ZH_MONTH_FROM_NOW}))\s*[月\-\/]\s*
    (${SUB_PATTERN_MONTH_DAY})[日|号]?
  description: |
    规则描述：日期识别规则，包括 MM月DD日、MM月DD号、MM-DD、MM/DD、上月DD日格式适配
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: now
    time_unit: day
    match_group:
      month: 1
      day: 2


- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_CONTEXT})\s*[月\-\/]\s*
    (${SUB_PATTERN_MONTH_DAY})[日|号]
  description: |
    规则描述：日期识别规则，包括当月X日、次月DD日格式适配。依赖上下文的，要放在后面
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: context
    time_unit: day
    match_group:
      month: 1
      day: 2


- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_CONTEXT})\s*月
  description: |
    规则描述：日期识别规则，包括当月、次月格式适配。依赖上下文的，要放在后面
  call_function: _handle_absolute_month
  parameters:
    type: absolute
    offset: context
    time_unit: month
    match_group:
      month: 1

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_CONTEXT})\s*(${PATTERN_ZH_COUNT})\s*(?:个)?月
  description: |
    规则描述：月数时间段识别规则，包括前X个月、后X月格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_month
  parameters:
    type: range
    offset: context
    time_unit: month
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_NOW})\s*(${PATTERN_ZH_COUNT})\s*(?:个)?月
  description: |
    规则描述：月数时间段识别规则，包括前X个月、后X月格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: month
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_CONTEXT})\s*(${PATTERN_ZH_COUNT})\s*[日|天]
  description: |
    规则描述：天数时间段识别规则，包括前X天、后X天格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_day
  parameters:
    type: range
    offset: context
    time_unit: day
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_NOW})\s*(${PATTERN_ZH_COUNT})\s*[日|天]
  description: |
    规则描述：天数时间段识别规则，包括过去X天、未来X天格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: day
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_CONTEXT})\s*年
  description: |
    规则描述：日期识别规则，包括当年、次年格式适配。依赖上下文的，要放在后面
  call_function: _handle_absolute_year
  parameters:
    type: absolute
    offset: context
    time_unit: year
    match_group:
      year: 1

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_CONTEXT})\s*(${PATTERN_ZH_COUNT})\s*(?:个)?(?:周|星期)
  description: |
    规则描述：周时间段识别规则，包括前X周、后X个星期格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_day
  parameters:
    type: range
    offset: context
    time_unit: week
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_NOW})\s*(${PATTERN_ZH_COUNT})\s*(?:个)?(?:周|星期)
  description: |
    规则描述：周时间段识别规则，包括前X周、后X个星期格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: week
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_WEEK_FROM_NOW})\s*(?:周|星期)(${PATTERN_WEEKDAY})
  description: |
    规则描述：星期识别规则，上周、下周、本周
  call_function: _handle_absolute_day
  parameters:
    type: absolute
    offset: now
    time_unit: week
    match_group:
      week: 1
      day: 2

- regex_detail: |
    (${PATTERN_ZH_WEEK_FROM_NOW})\s*(?:周|星期)
  description: |
    规则描述：星期识别规则，上周、下周、本周
  call_function: _handle_range_day
  parameters:
    type: range
    offset: now
    time_unit: week
    match_group:
      week: 1

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))\s*(?:年)\s*
    (${SUB_PATTERN_MONTH_DAY})(?:季度|[Qq])
  description: |
    规则描述：季度识别规则，包括 YYYY年X季度、今年X季度格式适配
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: quarter
    match_group:
      year: 1
      quarter: 2

- regex_detail: |
    (${SUB_PATTERN_YEAR})\s*(?:[Qq])\s*
    ([1-4])
  description: |
    规则描述：季度识别规则，2024Q4格式适配
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: quarter
    match_group:
      year: 1
      quarter: 2

- regex_detail: |
    ((?:${SUB_PATTERN_YEAR})|(?:${PATTERN_ZH_YEAR_FROM_NOW}))\s*(?:年)\s*
    (?:[Qq])(${SUB_PATTERN_MONTH_DAY})
  description: |
    规则描述：季度识别规则，包括 YYYY年QX格式适配
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: quarter
    match_group:
      year: 1
      quarter: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_NOW})\s*(${PATTERN_ZH_COUNT})\s*(?:个)?(?:季度|[Qq])
  description: |
    规则描述：季度时间段识别规则，包括前X季度、后X个季度格式适配。
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: quarter
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_KEYWORD_FROM_CONTEXT})\s*(${PATTERN_ZH_COUNT})\s*(?:个)?(?:季度|[Qq])
  description: |
    规则描述：季度时间段识别规则，包括未来X个月、之后X月格式适配。依赖上下文的，要放在后面
  call_function: _handle_range_month
  parameters:
    type: range
    offset: context
    time_unit: quarter
    match_group:
      keyword: 1
      offset: 2

- regex_detail: |
    ([1-4一二两三四])\s*(?:季度)
  description: |
    规则描述：季度识别规则
  call_function: _handle_range_month
  parameters:
    type: range
    offset: context
    time_unit: quarter
    match_group:
      quarter: 1

- regex_detail: |
    (${PATTERN_ZH_WEEK_FROM_NOW})\s*(?:季度|[Qq])
  description: |
    规则描述：季度识别规则，包括本季度、下季度格式适配。
  call_function: _handle_range_month
  parameters:
    type: range
    offset: now
    time_unit: quarter
    match_group:
      quarter: 1

- regex_detail: |
    (${SUB_PATTERN_YEAR})\s*年?
    ${PATTERN_CONNECTOR_RANGE}
    (${SUB_PATTERN_YEAR})\s*年
  description: |
    规则描述：年份识别规则，包括2024-2025年格式适配。
  call_function: _handle_range_year
  parameters:
    type: range
    offset: now
    time_unit: year
    match_group:
      fromYear: 1
      toYear: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_NOW})\s*年
    ${PATTERN_CONNECTOR_RANGE}
    (${PATTERN_ZH_YEAR_FROM_NOW})\s*年
  description: |
    规则描述：年份识别规则，包括今年至明年格式适配。
  call_function: _handle_range_year
  parameters:
    type: range
    offset: now
    time_unit: year
    match_group:
      fromYear: 1
      toYear: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_NOW})\s*年
    ${PATTERN_CONNECTOR_RANGE}
    (${SUB_PATTERN_YEAR})\s*年
  description: |
    规则描述：年份识别规则，包括今年-YYYY年格式适配。
  call_function: _handle_range_year
  parameters:
    type: range
    offset: now
    time_unit: year
    match_group:
      fromYear: 1
      toYear: 2

- regex_detail: |
    (${PATTERN_ZH_YEAR_FROM_NOW})\s*年
  description: |
    规则描述：日期识别规则，包括今年、明年格式适配。
  call_function: _handle_absolute_year
  parameters:
    type: absolute
    offset: now
    time_unit: year
    match_group:
      year: 1

- regex_detail: |
    (${SUB_PATTERN_YEAR})\s*年
  description: |
    规则描述：日期识别规则，包括今年、明年、YYYY年格式适配。
  call_function: _handle_absolute_year
  parameters:
    type: absolute
    offset: now
    time_unit: year
    match_group:
      year: 1

- regex_detail: |
    同比
  description: |
    规则描述：特殊规则，同比
  call_function: _handle_yoy_pop
  
- regex_detail: |
    ((?:周|月)?环比)
  description: |
    规则描述：特殊规则，同比
  call_function: _handle_yoy_pop