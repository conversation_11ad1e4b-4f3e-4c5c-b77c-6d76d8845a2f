# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Configuration load manager."""

from pathlib import Path
from higoalutils.utils.config_utils.operation import load_dotenv_file, load_yaml_file, apply_overrides
from typing import Any

from higoalutils.utils.config_utils.base import ConfigBase, T
from higoalutils.utils.config_utils.operation import (
    apply_overrides,
    load_dotenv_file,
    load_yaml_file,
)
from higoalutils.utils.config_utils.findpath import get_repo_root


class ConfigManager(ConfigBase[T]):
    """Configuration load manager."""

    def __init__(
        self,
        config_class: type[T],
        config_dir: str,
        config_file: str,
        overrides: dict[str, Any] | None = None,
    ):
        self._config_class = config_class
        self._config_dir = get_repo_root() / config_dir
        self._config_file = config_file
        self._overrides = overrides or {}
        self._instance: T | None = None

    def get_config(self) -> T:
        """Get the config."""
        if self._instance is None:
            self._instance = self._load_config()
        return self._instance

    def reload(self) -> T:
        """Reload the config."""
        self._instance = self._load_config()
        return self._instance

    def _load_config(self) -> T:
        config_path = Path(self._config_dir)
        load_dotenv_file(config_path)

        file_path = config_path / self._config_file
        raw_data = load_yaml_file(file_path)
        if self._overrides:
            apply_overrides(raw_data, self._overrides)

        return self._config_class(**raw_data)