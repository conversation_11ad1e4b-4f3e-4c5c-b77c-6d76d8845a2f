# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Config models."""

from higoalutils.config.models.base_config import BaseConfig as BaseConfig
from higoalutils.config.models.embedding_config import (
    EmbeddingConfig as EmbeddingConfig,
)
from higoalutils.config.models.langeuage_model_config import (
    LanguageModelConfig as LanguageModelConfig,
)
from higoalutils.config.models.model_config import (
    ModelRegistryConfig as ModelRegistryConfig,
)
from higoalutils.config.models.reporting_config import (
    ReportingConfig as ReportingConfig,
)
from higoalutils.config.models.system_config import SystemConfig as SystemConfig
