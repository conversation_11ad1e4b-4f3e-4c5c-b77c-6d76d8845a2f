
# Alembic Quick Start

本指南介绍如何在 HiGoalVault 项目中快速上手使用 Alembic 进行数据库结构管理和版本控制。

## 1️⃣ Alembic 是什么？

Alembic 是一个用于 SQL 数据库结构迁移的工具，支持版本控制。它的作用类似于 Git，用于管理数据库 schema 的每一次变更。

- 管理数据库结构（如表、字段、索引等）
- 支持从本地 CSV 初始化表结构
- 每次变更都会生成对应的迁移版本文件
- 所有版本记录保存在 `alembic/versions/` 中

## 2️⃣ 安装说明

在 `nl2sql/pyproject.toml` 中，已包含 Alembic 依赖。

执行以下命令安装开发环境时，Alembic 会自动安装：

```bash
cd nl2sql
uv sync
```
无需单独安装 Alembic。
如有修改需要，可以查看nl2sql\alembic.ini 和nl2sql\alembic\env.py

## 3️⃣ 使用方法

### 初始化数据库结构

首次使用或在数据库为空时执行：

```bash
alembic upgrade head
```

这将依次执行 `alembic/versions/` 中的所有版本脚本，将数据库升级至最新结构。

**常见bug：** MySQL默认是不接受导入本地数据的，需要在mysql的命令栏中手动设置(设置一次就行了).
```
SET GLOBAL local_infile = 1;
```


### 创建新版本

如果需要更改数据库结构（如新增列、修改字段类型）：

```bash
alembic revision -m "add new column to table" #在""内使用合适的备注
```
使用此命令后，在alembic/versions会生成一下新的脚本,名字类似于 xxxxxx_add_trade_calendar_table.py
此文件中会有def upgrade() 和 def downgrade().
你需要手动编辑，定义新的建表或修改操作。

### 自动生成结构变更脚本（高级用法）

若已将 SQLAlchemy 模型正确绑定到 Alembic 的 `target_metadata`，可以使用：

```bash
alembic revision --autogenerate -m "update structure"
```

此功能需先配置 `env.py` 文件以识别你的模型元数据。
目前本repo还没有做到这一步，可以按照自己的需求修改代码使用。

## 4️⃣ 注意事项

- Alembic **只管理数据库结构**，**不会处理具体数据内容**。
- 如果只是更换了 CSV 数据文件，而结构未变，执行 `alembic upgrade head` 不会重新导入数据。
- 如需导入新数据，请编写专门的数据加载脚本。

## ✅ 总结

| 操作 | 命令 |
|------|------|
| 初始化数据库结构 | `alembic upgrade head` |
| 创建结构迁移版本 | `alembic revision -m "message"` |
| 自动检测结构变更 | `alembic revision --autogenerate -m "message"` |

确保每次结构变动都有版本记录，以方便追溯和回退。
