# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Null database engine implementation."""

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeMeta

from higoalutils.database.relational_database.base import DatabaseEngineBase


class NullDatabaseEngine(DatabaseEngineBase):
    """Null database engine implementation."""

    def get_engine(self) -> AsyncEngine:
        """Get the engine."""
        msg = "NullDatabaseEngine has no engine."
        raise NotImplementedError(msg)

    def get_sessionmaker(self) -> async_sessionmaker[AsyncSession]:
        """Get the sessionmaker."""
        msg = "NullDatabaseEngine has no sessionmaker."
        raise NotImplementedError(msg)
    
    def get_base(self) -> DeclarativeMeta:
        """Get the base."""
        msg = "NullDatabaseEngine has no base."
        raise NotImplementedError(msg)

    async def warmup(self) -> None:
        """Warmup the database engine."""
        return

    async def close(self) -> None:
        """Close the database engine."""
        return