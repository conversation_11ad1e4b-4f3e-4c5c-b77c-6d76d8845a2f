# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Chat-based OpenAI LLM implementation."""

import json
import logging
import time
from collections.abc import AsyncGenerator, Generator
from typing import Any

from tenacity import (
    AsyncRetrying,
    RetryError,
    Retrying,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential_jitter,
)

from higoalutils.cache.pipeline_cache import Pipeline<PERSON>ache
from higoalutils.language_model.llm.base import BaseLLM, BaseLLMCallback
from higoalutils.language_model.llm.oai.base import OpenAILLMImpl
from higoalutils.language_model.llm.oai.text_utils import num_tokens
from higoalutils.language_model.llm.oai.typing import (
    OPENAI_RETRY_ERROR_TYPES,
    OpenaiApiType,
)
from higoalutils.logger.base import StatusLogger

log = logging.getLogger(__name__)

_MODEL_REQUIRED_MSG = "model is required"


class ChatOpenAI(BaseLLM, OpenAILLMImpl):
    """Wrapper for OpenAI ChatCompletion models."""

    def __init__(
        self,
        encoding_model: str,
        api_key: str | None = None,
        model: str | None = None,
        api_base: str | None = None,
        api_type: OpenaiApiType = OpenaiApiType.OpenAI,
        max_retries: int = 10,
        request_timeout: float = 180.0,
        retry_error_types: tuple[type[BaseException]] = OPENAI_RETRY_ERROR_TYPES,  # type: ignore
        logger: StatusLogger | None = None,
        max_connections: int = 100,  # 新增参数
    ):
        OpenAILLMImpl.__init__(
            self=self,
            api_key=api_key,
            api_base=api_base,
            api_type=api_type,  # type: ignore
            max_retries=max_retries,
            request_timeout=request_timeout,
            logger=logger,
            max_connections=max_connections,  # 传递连接数参数
        )
        self.model = model
        self.retry_error_types = retry_error_types
        self.encoding_model = encoding_model

    def generate(
        self,
        messages: str | list[Any],
        streaming: bool = True,
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> str:
        """Generate text."""
        t1 = time.time()
        try:
            retryer = Retrying(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential_jitter(max=10),
                reraise=True,
                retry=retry_if_exception_type(self.retry_error_types),
            )
            for attempt in retryer:
                with attempt:
                    return self._generate(
                        messages=messages,
                        streaming=streaming,
                        callbacks=callbacks,
                        **kwargs,
                    )
        except RetryError as e:
            self._reporter.error(
                message="Error at generate()", details={self.__class__.__name__: str(e)}
            )
            log.exception(f"Victor Wu: Error at generate(): {e}")  # noqa: G004, TRY401
            time.time()
            completion_time = time.time() - t1
            if callbacks:
                if isinstance(messages, list):
                    prompt = "\n".join([message["content"] for message in messages if "content" in message])
                else:
                    prompt = messages if isinstance(messages, str) else ""
                for callback in callbacks:
                    callback.on_llm_query(
                        prompt=prompt,
                        exception=f"Error at generate(): {e}",
                        completion_time=completion_time,
                        llm_calls=1,
                    )
            return ""
        else:
            # TODO: why not just throw in this case?
            time.time()
            completion_time = time.time() - t1
            error_msg = "Unexpected error in generate()"

            self._reporter.error(error_msg)
            msg = f"Victor Wu: {error_msg}"
            log.error(msg)
            if callbacks:
                if isinstance(messages, list):
                    prompt = "\n".join([message["content"] for message in messages if "content" in message])
                else:
                    prompt = messages if isinstance(messages, str) else ""
                for callback in callbacks:
                    callback.on_llm_query(
                        prompt=prompt,
                        exception=f"Error at generate(): {error_msg}",
                        completion_time=completion_time,
                        llm_calls=1,
                    )
            return ""

    def stream_generate(
        self,
        messages: str | list[Any],
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> Generator[str, None, None]:
        """Generate text with streaming."""
        try:
            retryer = Retrying(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential_jitter(max=10),
                reraise=True,
                retry=retry_if_exception_type(self.retry_error_types),
            )
            for attempt in retryer:
                with attempt:
                    generator = self._stream_generate(
                        messages=messages,
                        callbacks=callbacks,
                        **kwargs,
                    )
                    yield from generator

        except RetryError as e:
            self._reporter.error(
                message="Error at stream_generate()",
                details={self.__class__.__name__: str(e)},
            )
            return
        else:
            return

    async def agenerate(
        self,
        messages: str | list[Any],
        streaming: bool = True,
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> str:
        """Generate text asynchronously."""
        t1 = time.time()
        try:
            retryer = AsyncRetrying(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential_jitter(max=10),
                reraise=True,
                retry=retry_if_exception_type(self.retry_error_types),  # type: ignore
            )
            async for attempt in retryer:
                with attempt:
                    return await self._agenerate(
                        num_tokens=num_tokens,
                        messages=messages,
                        streaming=streaming,
                        callbacks=callbacks,
                        **kwargs,
                    )
        except RetryError as e:
            self._reporter.error(f"Error at agenerate(): {e}")
            msg = f"Victor Wu: Error at agenerate(): {e}"
            log.exception(msg)
            time.time()
            completion_time = time.time() - t1
            if callbacks:
                if isinstance(messages, list):
                    prompt = "\n".join([message["content"] for message in messages if "content" in message])
                else:
                    prompt = messages if isinstance(messages, str) else ""
                for callback in callbacks:
                    await callback.async_on_llm_query(
                        prompt=prompt,
                        exception=f"Error at agenerate(): {e}",
                        completion_time=completion_time,
                        llm_calls=1,
                    )
            return ""
        else:
            # TODO: why not just throw in this case?
            time.time()
            completion_time = time.time() - t1
            error_msg = "Unexpected error in agenerate()"

            self._reporter.error(error_msg)
            msg = f"Victor Wu: {error_msg}"
            log.error(msg)
            if callbacks:
                if isinstance(messages, list):
                    prompt = "\n".join([message["content"] for message in messages if "content" in message])
                else:
                    prompt = messages if isinstance(messages, str) else ""
                for callback in callbacks:
                    await callback.async_on_llm_query(
                        prompt=prompt,
                        exception=f"Error at agenerate(): {error_msg}",
                        completion_time=completion_time,
                        llm_calls=1,
                    )
            return ""

    async def astream_generate( #  type: ignore
        self,
        messages: str | list[Any],
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        """Generate text asynchronously with streaming."""
        try:
            retryer = AsyncRetrying(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential_jitter(max=10),
                reraise=True,
                retry=retry_if_exception_type(self.retry_error_types),  # type: ignore
            )
            async for attempt in retryer:
                with attempt:
                    inner_generator = self._astream_generate(
                        messages=messages,
                        callbacks=callbacks,
                        **kwargs,
                    )
                    async for response in inner_generator:
                        yield response
        except RetryError as e:
            self._reporter.error(f"Error at astream_generate(): {e}")
            return
        else:
            return

    def _generate(
        self,
        messages: str | list[Any],
        streaming: bool = False,
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> str:
        t1 = time.time()
        model = self.model
        if not model:
            raise ValueError(_MODEL_REQUIRED_MSG)
        
        # 定义允许的 kwargs 参数列表
        allowed_kwargs = {
            "temperature",
            "max_tokens",
            "top_p",
            "frequency_penalty",
            "presence_penalty",
            "stop",
            "response_format",
            "isJson",
        }

        # 过滤掉无效的 kwargs 参数
        invalid_kwargs = set(kwargs.keys()) - allowed_kwargs
        if invalid_kwargs:
            kwargs = {k: v for k, v in kwargs.items() if k in allowed_kwargs}

        # 获取 isJson 参数
        is_json = kwargs.get("isJson", False)

        # 如果是 JSON 格式请求, 并且没有提供 response_format, 则自动添加
        if is_json and not kwargs.get("response_format",None):
            kwargs["response_format"] = {"type": "json_object"}

        kwargs.pop("isJson", None) # 移除 isJson 参数
        
        response = self.sync_client.chat.completions.create( # type: ignore
            model=model,
            messages=messages,  # type: ignore
            stream=streaming,
            **kwargs,
        )
        if streaming:
            full_response = ""
            while True:
                try:
                    chunk = response.__next__()  # type: ignore
                    if not chunk or not chunk.choices:
                        continue

                    delta = (
                        chunk.choices[0].delta.content
                        if chunk.choices[0].delta and chunk.choices[0].delta.content
                        else ""
                    )  # type: ignore

                    full_response += delta
                    if callbacks:
                        for callback in callbacks:
                            callback.on_llm_new_token(delta)
                    if chunk.choices[0].finish_reason == "stop":  # type: ignore
                        break
                except StopIteration:
                    break
            t2 = time.time()
            completion_time = t2 - t1
            if callbacks:
                if isinstance(messages, list):
                    prompt = "\n".join([message["content"] for message in messages if "content" in message])
                else:
                    prompt = messages if isinstance(messages, str) else ""
                for callback in callbacks:
                    callback.on_llm_query(
                        prompt=prompt,
                        response=full_response,
                        completion_time=completion_time,
                        llm_calls=1,
                    )
            return full_response
        
        result = response.choices[0].message.content or ""
        t2 = time.time()
        completion_time = t2 - t1

        if callbacks:
            if isinstance(messages, list):
                prompt = "\n".join([message["content"] for message in messages if "content" in message])
            else:
                prompt = messages if isinstance(messages, str) else ""
            for callback in callbacks:
                callback.on_llm_query(
                    prompt=prompt,
                    response=result,
                    completion_time=completion_time,
                    llm_calls=1,
                    prompt_tokens=response.usage.prompt_tokens if response.usage else 0,
                    output_tokens=response.usage.completion_tokens if response.usage else 0
                )
        return result


    def _stream_generate(
        self,
        messages: str | list[Any],
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> Generator[str, None, None]:
        model = self.model
        if not model:
            raise ValueError(_MODEL_REQUIRED_MSG)
        response = self.sync_client.chat.completions.create(  # type: ignore
            model=model,
            messages=messages,  # type: ignore
            stream=True,
            **kwargs,
        )
        for chunk in response:
            if not chunk or not chunk.choices:
                continue

            delta = (
                chunk.choices[0].delta.content
                if chunk.choices[0].delta and chunk.choices[0].delta.content
                else ""
            )

            yield delta

            if callbacks:
                for callback in callbacks:
                    callback.on_llm_new_token(delta)

    async def _agenerate(
        self,
        messages: str | list[Any],
        streaming: bool = True,
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> str:
        t1 = time.time()
        model = self.model
        if not model:
            raise ValueError(_MODEL_REQUIRED_MSG)

        # 定义允许的 kwargs 参数列表
        allowed_kwargs = {
            "temperature",
            "max_tokens",
            "top_p",
            "frequency_penalty",
            "presence_penalty",
            "stop",
            "response_format",
            "isJson",
        }

        # 过滤掉无效的 kwargs 参数
        invalid_kwargs = set(kwargs.keys()) - allowed_kwargs
        if invalid_kwargs:
            kwargs = {k: v for k, v in kwargs.items() if k in allowed_kwargs}

        # 获取 isJson 参数
        is_json = kwargs.get("isJson", False)

        # 如果是 JSON 格式请求, 并且没有提供 response_format, 则自动添加
        if is_json and not kwargs.get("response_format",None):
            kwargs["response_format"] = {"type": "json_object"}

        kwargs.pop("isJson", None) # 移除 isJson 参数

        response = await self.async_client.chat.completions.create(  # type: ignore
            model=model,
            messages=messages,  # type: ignore
            stream=streaming,
            **kwargs,
        )
        if streaming:
            full_response = ""
            while True:
                try:
                    chunk = await response.__anext__()  # type: ignore
                    if not chunk or not chunk.choices:
                        continue

                    delta = (
                        chunk.choices[0].delta.content
                        if chunk.choices[0].delta and chunk.choices[0].delta.content
                        else ""
                    )  # type: ignore

                    full_response += delta
                    if callbacks:
                        for callback in callbacks:
                            await callback.async_on_llm_new_token(delta)
                    if chunk.choices[0].finish_reason == "stop":  # type: ignore
                        break
                except StopIteration:
                    break
            t2 = time.time()
            completion_time = t2 - t1
            if callbacks:
                if isinstance(messages, list):
                    prompt = "\n".join([message["content"] for message in messages if "content" in message])
                else:
                    prompt = messages if isinstance(messages, str) else ""
                for callback in callbacks:
                    await callback.async_on_llm_query(
                        prompt=prompt,
                        response=full_response,
                        completion_time=completion_time,
                        llm_calls=1,
                    )
            msg = f"Victor Wu: _agenerate(): full_response={full_response}"
            log.info(msg)
            return full_response
        msg = f"Victor Wu: _agenerate(): response.choices[0].message.content={response.choices[0].message.content}"
        log.info(msg)
        result = response.choices[0].message.content or ""

        t2 = time.time()
        completion_time = t2 - t1

        if callbacks:
            if isinstance(messages, list):
                prompt = "\n".join([message["content"] for message in messages if "content" in message])
            else:
                prompt = messages if isinstance(messages, str) else ""
            for callback in callbacks:
                callback.on_llm_query(
                    prompt=prompt,
                    response=result,
                    completion_time=completion_time,
                    llm_calls=1,
                    prompt_tokens=response.usage.prompt_tokens if response.usage else 0,
                    output_tokens=response.usage.completion_tokens if response.usage else 0
                )
        return result

    async def _astream_generate(
        self,
        messages: str | list[Any],
        callbacks: list[BaseLLMCallback] | None = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        model = self.model
        if not model:
            raise ValueError(_MODEL_REQUIRED_MSG)
        response = await self.async_client.chat.completions.create(  # type: ignore
            model=model,
            messages=messages,  # type: ignore
            stream=True,
            **kwargs,
        )
        async for chunk in response:
            if not chunk or not chunk.choices:
                continue

            delta = (
                chunk.choices[0].delta.content
                if chunk.choices[0].delta and chunk.choices[0].delta.content
                else ""
            )  # type: ignore

            yield delta

            if callbacks:
                for callback in callbacks:
                    callback.on_llm_new_token(delta)
    
    async def agenerate_json(
        self,
        messages: str | list[Any],
        streaming: bool = False,
        callbacks: list[BaseLLMCallback] | None = None,
        isJson: bool = True,  # noqa: N803
        cache: PipelineCache | None = None,
    ) -> dict | str:
        """LLM 访问函数。允许带cache和json解析."""
        if not messages:
            msg = "Messages is empty!"
            raise ValueError(msg)

        if cache:
            # Handle both string and list message formats for cache key creation
            if isinstance(messages, str):
                cache_key = cache.create_key(messages)
            else:
                # messages is a list, use the content of the first message
                cache_key = cache.create_key(messages[0]["content"])
            if await cache.has(cache_key):
                result = await cache.get(cache_key)
                return json.loads(result)

        response = await self.agenerate(
            messages,
            streaming=streaming,
            callbacks=callbacks,
            isJson=isJson,
        )
        from higoalutils.language_model.llm.parse_json import try_parse_json_object

        if isJson:
            _, response = try_parse_json_object(response)

        if cache and response:
            await cache.set(cache_key, json.dumps(response, ensure_ascii=False, indent=4)) # type: ignore
            
        if not response:
            log.error("Failed to parse the response from the LLM")
            msg = "Failed to parse the response from the LLM"
            raise ValueError(msg)
            
        return response
