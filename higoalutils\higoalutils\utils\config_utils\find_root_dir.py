# Copyright (c) 2025 HiGoal Corporation.
# Proprietary software. All rights reserved.

"""Find the root directory of the project."""

from pathlib import Path


def find_root_dir(marker="datavolume") -> str:
    """Find the root directory of the project."""
    absolute_root = Path(__file__).resolve()
    while absolute_root != absolute_root.parent:
        if (absolute_root / marker).exists():
            return str(absolute_root)
        absolute_root = absolute_root.parent
    return str(absolute_root)