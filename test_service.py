#!/usr/bin/env python3
"""
测试 nl2sql.database.service 模块的脚本
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_get_tusharing_fut_daily():
    """测试 get_tusharing_fut_daily 函数"""
    try:
        # 导入必要的模块
        from nl2sql.database.service import get_tusharing_fut_daily
        from higoalutils.config.preload import preload_utils
        
        print("正在初始化系统...")
        await preload_utils.start_up()
        
        print("正在调用 get_tusharing_fut_daily...")
        result = await get_tusharing_fut_daily('20250401', '20250104', 'asd', '+09:00')
        
        print("结果:")
        print(result)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("正在清理系统...")
        await preload_utils.clean_up()

if __name__ == "__main__":
    asyncio.run(test_get_tusharing_fut_daily())
