# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""HuggingFaceEmbeddings model definition."""

import logging
import os
from pathlib import Path

from huggingface_hub import snapshot_download
from langchain_huggingface import HuggingFaceEmbeddings

from higoalutils.config.load_model_info import get_model_info

logger = logging.getLogger(__name__)
os.environ["TOKENIZERS_PARALLELISM"] = "false"

def sanitize_model_dir(model_name: str) -> str:
    """将 HuggingFace 模型名转换为合法的本地目录名."""
    return model_name.replace("/", "__")

def get_embeddings(
    modelname: str = "BAAI/bge-large-zh-v1.5",
    device: str = "cpu"
) -> HuggingFaceEmbeddings:
    """
    加载或自动下载 HuggingFace 模型用于向量嵌入.

    参数:
        modelname (str): 模型名称, 例如 'BAAI/bge-large-zh-v1.5' 或 'your_custom_model_dir'
        device (str): 运行设备, 支持 'cpu', 'cuda', 'mps' 等

    返回:
        HuggingFaceEmbeddings 实例
    """
    embedding_model = get_model_info().get_by_model_name(modelname)
    local_path = embedding_model.local_path if embedding_model and embedding_model.local_path else None

    is_baai_model = modelname.startswith("BAAI/")

    if local_path:
        cache_root = Path(local_path).resolve()
        Path(cache_root).mkdir(parents=True, exist_ok=True)

        if is_baai_model:
            # 原逻辑: BAAI 模型自动下载并缓存
            local_dir = cache_root / sanitize_model_dir(modelname)
            if not Path(local_dir / "config.json").exists():
                logger.info("Downloading model: %s to %s", modelname, local_dir)
                snapshot_download(
                    repo_id=modelname,
                    local_dir=local_dir,
                    local_dir_use_symlinks=False,
                )
        else:
            # 自定义模型: 直接按模型名拼接路径  # noqa: ERA001
            local_dir = cache_root / modelname
            if not Path(local_dir).exists():
                msg = f"Custom model directory not found: {local_dir}"
                raise FileNotFoundError(msg)

        os.environ["SENTENCE_TRANSFORMERS_HOME"] = str(cache_root)
        model_path = local_dir
    else:
        # 没有配置本地路径, 直接从 HF 缓存中加载
        logger.info("Using default HuggingFace cache to load model: %s", modelname)
        model_path = modelname

    return HuggingFaceEmbeddings(
        model_name=str(model_path),
        model_kwargs={"device": device},
        encode_kwargs={"normalize_embeddings": True},
    )