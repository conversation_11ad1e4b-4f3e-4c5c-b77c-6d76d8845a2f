# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import String, Integer, Boolean, Time, Date, ForeignKey, Text,TIMESTAMP, func, DateTime,BigInteger,DECIMAL
from higoalutils.database.relational_database.manager import DBEngineManager
from datetime import datetime

Base = DBEngineManager().get_base()

class Exchange(Base):
    __tablename__ = "exchange"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(10), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)

    symbols: Mapped[list["Symbol"]] = relationship("Symbol", back_populates="exchange")
    trading_windows: Mapped[list["TradingWindow"]] = relationship("TradingWindow", back_populates="exchange")

class Symbol(Base):
    __tablename__ = "symbol"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    exchange_id: Mapped[int] = mapped_column(ForeignKey("exchange.id"), nullable=False)
    prefix: Mapped[str] = mapped_column(String(20), nullable=False)
    description: Mapped[str] = mapped_column(String(100), nullable=False)
    special: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    exchange: Mapped[Exchange] = relationship("Exchange", back_populates="symbols")
    windows: Mapped[list["TradingWindow"]] = relationship(
        "TradingWindow",
        secondary="window_symbol_map",
        back_populates="symbols"
    )

class TradingWindow(Base):
    __tablename__ = "trading_window"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    exchange_id: Mapped[int] = mapped_column(ForeignKey("exchange.id"), nullable=False)
    session: Mapped[str] = mapped_column(String(20), nullable=False)
    start_time: Mapped[Time] = mapped_column(Time, nullable=False)
    end_time: Mapped[Time] = mapped_column(Time, nullable=False)

    exchange: Mapped[Exchange] = relationship("Exchange", back_populates="trading_windows")
    symbols: Mapped[list[Symbol]] = relationship(
        "Symbol",
        secondary="window_symbol_map",
        back_populates="windows"
    )

class WindowSymbolMap(Base):
    __tablename__ = "window_symbol_map"
    window_id: Mapped[int] = mapped_column(ForeignKey("trading_window.id"), primary_key=True)
    prefix: Mapped[str] = mapped_column(ForeignKey("symbol.prefix"), primary_key=True)

class TradeCalendar(Base):
    __tablename__ = "trade_calendar"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    exchange: Mapped[str] = mapped_column(String(10), nullable=False, index=True)
    cal_date: Mapped[Date] = mapped_column(Date, nullable=False, index=True)  # format: YYYYMMDD or Date
    is_open: Mapped[bool] = mapped_column(Boolean, nullable=False)
    pretrade_date: Mapped[Date] = mapped_column(Date, nullable=True)

class QueryAnswerLog(Base):
    __tablename__ = "query_answer_log"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    user_id: Mapped[str] = mapped_column(String(1024), nullable=False)
    query: Mapped[str] = mapped_column(Text, nullable=False)
    answer: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)

class Tusharing_Fut_Daily(Base):
    __tablename__ = "ods_tushare_stockoption_fut_daily_d"
    
    # 主键字段
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True, comment="自增主键ID")
    # 业务字段
    ts_code: Mapped[str] = mapped_column(String(50), nullable=False, comment="TS合约代码")
    trade_date: Mapped[str] = mapped_column(String(16), nullable=False, comment="交易日期（YYYYMMDD）")
    time_zone: Mapped[str] = mapped_column(String(8), nullable=False, comment="时区")
    pre_close: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="昨收盘价")
    pre_settle: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="昨结算价")
    open: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="开盘价")
    high: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="最高价")
    low: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="最低价")
    close: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="收盘价")
    settle: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="结算价")
    change1: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="涨跌1（收盘价-昨结算价）")
    change2: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="涨跌2（结算价-昨结算价）")
    vol: Mapped[int] = mapped_column(BigInteger, comment="成交量(手)")
    amount: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="成交金额(万元)")
    oi: Mapped[int] = mapped_column(BigInteger, comment="持仓量(手)")
    oi_chg: Mapped[int] = mapped_column(BigInteger, comment="持仓量变化")
    delv_settle: Mapped[float] = mapped_column(DECIMAL(20, 4), comment="交割结算价")
    # 系统字段
    create_time: Mapped[datetime] = mapped_column(TIMESTAMP, server_default=func.now(), nullable=False, comment="创建时间")
    update_time: Mapped[datetime] = mapped_column(
        TIMESTAMP, 
        server_default=func.now(), 
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    is_delete: Mapped[bool] = mapped_column(server_default=text("0"), nullable=False, comment="是否删除(0:未删除,1:已删除)")
    updated_by: Mapped[str] = mapped_column(String(50), nullable=False, comment="最后修改人")

