import sys
from higoalutils.utils.config_utils.findpath import get_repo_root
root = get_repo_root()
print(root)
sys.path.insert(0, root)
from nl2sql.core.get_data.get_data_ts import TushareDataFetcher
TUSHARE_FETCHER = TushareDataFetcher()

param_dict = {'general_info': {'symbol': None, 'item_description': '石油沥青', 'region': None, 'symbol_suffix': '主力合约', 'intention': '历史价格', 'event': None, 'time_anchor': 'current', 'time_span': '7 day', 'time_step': '1 min'}, 
              'item_description': '石油沥青', 
              'prefix': 'bu', 
              'symbol_prefix': 'bu', 
              'exchange_code': 'SHFE', 
              'symbol_suffix': '主力合约', 
              'todate': '2025-07-29', 
              'fromdate': '2025-07-22', 
              'time_step': '1 min', 
              'symbol': 'bu主力合约'}

if __name__ == "__main__":
    data = TUSHARE_FETCHER._get_daily_kline(param_dict)
    print(data)