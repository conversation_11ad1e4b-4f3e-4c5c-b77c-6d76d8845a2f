# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Database lifecycle."""

from higoalutils.database.relational_database.manager import DBEngineManager
from higoalutils.utils.singleton_utils.registry import destroy_instance


async def init_relational_db() -> None:
    """Initialize relational database."""
    db = DBEngineManager()
    await db.warmup()
    destroy_instance(db)

async def shutdown_relational_db() -> None:
    """Shutdown relational database."""
    db = DBEngineManager()
    await db.close()
    destroy_instance(db)