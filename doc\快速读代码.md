## 快速查看相关文件
mysql数据库定义位置：datavolume\config
future数据：datavolume\database\future 
ORM class 位置：nl2sql\database\models.py
和数据库交互函数位置：nl2sql\database\service.py

## 已有的表格：
exchange 交易所的代码匹配交易种类代码prefix ，不同的调用中交易所代码可能不同，exchange_code, exchange_tushare ...
symbol 交易种类prefix代码，中文描述，交易所，是否为特殊品类
trading_window 交易时间窗口
window_symbol_map 交易时间窗口和prefix的匹配，以此可以通过prefix找到所有的交易窗口。
trade_calendar 交易日历，各个交易所某天是否为交易日，上一个交易日。（这里有bug，有两类交易所代码和exchange匹配不上，需要问tushare客服，此数据从tushare扒得）

## 扒数据
和数据源交互都在此路径下: nl2sql\core\get_data
tushare: get_data_ts.py
天勤：get_data_tq.py
新浪：get_data_sina.py
这里的函数大多都是作为参考，a quick start, 并不一定要完全遵循。tushare的完成度高一点，但也有待更新.
去对照他们的API接口文档做更完备的数据抓取工作。
而且需要自动化，后台一直做抓取+迁移工作。

### 接口文档
#### 1. tushare(优先级最高)
官方链接 http://tushare.pro/
数据接口 https://tushare.pro/document/2
股票行情数据 https://tushare.pro/document/2?doc_id=15
#### 2. 天勤TQSDK（主要抓实时数据，分时线）
清洗后的历史与实时数据
官方链接 https://www.shinnytech.com/
实时行情数据 https://doc.shinnytech.com/tqsdk/latest/quickstart.html#quickstart-1
K线序列数据(会跟实时行情一起同步自动更新) https://doc.shinnytech.com/tqsdk/latest/quickstart.html#k
#### 3. 新浪（优先级低）
各类市场数据基础https://finance.sina.com.cn/ 数据，返回JSON
官方链接 https://finance.sina.com.cn/
行情中心 https://vip.stock.finance.sina.com.cn/mkt/
野生整理的股票接口https://blog.csdn.net/yu502586512/article/details/134964877
野生整合的开源项目 https://github.com/shidenggui/easyquotation
野生爬取及处理开源示例 https://github.com/DemonDamon/FinnewsHunter

## 其他
数据库迁移代码，但未经验证，有bug, 仅仅作为参考 nl2sql\sync_database.py

## 启动程序tushare
测试抓取tushare日K: tests\get_data_test.py
可以直接运行，如果出现找不到包等错误可以尝试用命令行：
```
# 确保你在根目录下，而非nl2sql目录.
python -m tests.get_data_test
```
