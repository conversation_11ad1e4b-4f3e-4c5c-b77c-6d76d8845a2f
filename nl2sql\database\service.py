# service.py
from datetime import datetime,timedelta
import pytz
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import insert
from sqlalchemy.orm import selectinload
from typing import List, Tuple, Optional
from datetime import date
from nl2sql.database.models import (
    Exchange, Symbol, TradingWindow, WindowSymbolMap,TradeCalendar, QueryAnswerLog,Tusharing_Fut_Daily
)

async def get_symbol_by_prefix(session: AsyncSession, prefix: str) -> Optional[Symbol]:
    result = await session.execute(
        select(Symbol)
        .options(selectinload(Symbol.exchange))
        .where(Symbol.prefix == prefix)
    )
    return result.scalar_one_or_none()

async def get_exchange_by_code(session: AsyncSession, code: str) -> Optional[Exchange]:
    result = await session.execute(
        select(Exchange)
        .options(
            selectinload(Exchange.symbols),
            selectinload(Exchange.trading_windows)
        )
        .where(Exchange.code == code)
    )
    return result.scalar_one_or_none()

async def get_trading_windows_for_prefix(session: AsyncSession, prefix: str) -> List[dict]:
    """
    Given a symbol prefix, find all trading windows (id, session, start_time, end_time) for that prefix.
    """
    # 1. Find all window ids for this prefix
    result = await session.execute(
        select(WindowSymbolMap.window_id)
        .where(WindowSymbolMap.prefix == prefix)
    )
    window_ids = [row[0] for row in result.fetchall()]
    if not window_ids:
        return []

    # 2. Get all trading window details for these ids
    result = await session.execute(
        select(TradingWindow)
        .where(TradingWindow.id.in_(window_ids))
    )
    windows = result.scalars().all()
    # 3. Format the response
    return [
        {
            "id": w.id,
            "session": w.session,
            "start_time": str(w.start_time),
            "end_time": str(w.end_time),
        }
        for w in sorted(windows, key=lambda x: (x.start_time, x.end_time))
    ]

async def get_trading_window_by_id(session: AsyncSession, window_id: int) -> Optional[TradingWindow]:
    result = await session.execute(
        select(TradingWindow)
        .where(TradingWindow.id == window_id)
    )
    return result.scalar_one_or_none()


async def get_symbol_info(
    session: AsyncSession,
    prefix: str = None,
    description: str = None
) -> Optional[dict]:
    """
    Query symbol info from symbol table by prefix or description. 
    Returns dict: {exchange_code, prefix, description, special}
    """

    if prefix is not None:
        result = await session.execute(
            select(Symbol)
            .options(selectinload(Symbol.exchange))
            .where(Symbol.prefix == prefix)
        )
    elif description is not None:
        result = await session.execute(
            select(Symbol)
            .options(selectinload(Symbol.exchange))
            .where(Symbol.description == description)
        )
    else:
        return None

    symbol = result.scalar_one_or_none()
    if not symbol:
        return None

    return {
        "exchange_code": symbol.exchange.code if symbol.exchange else None,
        "prefix": symbol.prefix,
        "description": symbol.description,
        "special": symbol.special
    }


async def get_trade_calendar_day(
    session: AsyncSession,
    exchange: str,
    check_date: date
) -> Optional[TradeCalendar]:
    """
    Fetch the TradeCalendar ORM row for given exchange and date.
    """
    result = await session.execute(
        select(TradeCalendar)
        .where(
            TradeCalendar.exchange == exchange,
            TradeCalendar.cal_date == check_date
        )
    )
    trade_date_info = result.scalar_one_or_none()
    if not trade_date_info:
        return None
    return {
        "exchange": trade_date_info.exchange,
        "cal_date": trade_date_info.cal_date,
        "is_open": trade_date_info.is_open,
        "pretrade_date": trade_date_info.pretrade_date
    }

async def insert_qa_record(
    session: AsyncSession,
    query: str,
    answer: str
):
    await session.execute(
        insert(QueryAnswerLog).values(
            query=query,
            answer=answer,
            user_id="admin"
        )
    )


async def get_tusharing_fut_daily(
    session: AsyncSession,
    start_trade_date: str,
    end_trade_date: str,
    ts_code: str,
    time_zone:str
):
    """
    """
    #计算时差
    sign = 1 if time_zone.startswith('+') else -1
    hours, minutes = map(int, time_zone[1:].split(':'))
    offset_hours = sign * (hours + minutes/60)
    # print(sign,'sign',hours,'hours',minutes,'minutes',offset_hours,'offset_hours')
    time_diff = 8 - offset_hours 

    start_trade_date = datetime.strptime(start_trade_date, "%Y%m%d")
    end_trade_date = datetime.strptime(end_trade_date, "%Y%m%d")

    start_trade_date = (start_trade_date - timedelta(hours=time_diff)).strftime("%Y-%m-%d")
    end_trade_date = (end_trade_date - timedelta(hours=time_diff)).strftime("%Y-%m-%d")

    print(start_trade_date,end_trade_date)

    data = select(Tusharing_Fut_Daily).where(
        and_(
            Tusharing_Fut_Daily.ts_code == ts_code,
            Tusharing_Fut_Daily.trade_date >= start_trade_date,
            Tusharing_Fut_Daily.trade_date <= end_trade_date
        )
    )
    
    # 执行查询
    result = await session.execute(data)
    return result.scalars().all()


get_tusharing_fut_daily('20250401','20250104','asd','+09:00')
