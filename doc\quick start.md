
## Quick Start
HiGoalVault Database快速入门. 本项目为 HiGoalVault的关键部分，本文档仅用于说明如何搭建数据库相关的开发环境。后续可基于此进行数据查询等工作。
### requirements:
操作系统：Windows 10 及以上（推荐）

Python 版本：>= 3.10 (推荐3.12)

虚拟环境：推荐使用 uv 创建（非强制）

### 1. installation with uv
uv是一个帮助控制虚拟环境的工具，如果你的电脑上没有使用过uv，需要进行安装.
打开命令栏窗口：
```
# On Windows
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
# On macOS and Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### create virtual environment with uv.
打开terminal, 默认起始路径在此repo的根目录下.
```
# 通过uv创建虚拟环境
# 如果目录下有.python-version指定了python版本，会自动在当前环境下查找有没有指定版本的python可执行文件，
# 如果你使用了conda虚拟环境，先退出: conda deactivate
# 如果发现系统没有这个版本的python环境，就会下载并且缓存在uv中
# 如果没有指定版本，那么就使用当前环境的python版本，并引用其可执行文件
# 详见：https://docs.astral.sh/uv/getting-started/features/

cd nl2sql #确保在正确的文件夹内，有pyproject.toml文件
uv venv
# 激活虚拟环境
# On Windows
.venv\Scripts\activate
# On macOS and Linux
source .venv/bin/activate
```
#### 同步虚拟环境的包
```
# 确保你在 nl2sql目录下，有pyproject.toml文件
uv sync
```
**到这里你的nl2sql虚拟环境应该已经完成了。**
#### (后期开发可选)manage virtual environment with uv.
第一次安装时可以略过此步骤，如果后期开发时发现自己要安装/卸载某些单独的package,用以下命令。
 For venv created with uv, later on if you want to add more package, use ***uv add \<your-package-name\>*** 
 To remove pacakges use ***uv remove \<your-package-name\>***

### 2. Install higoalutils
higoalutils是HiGoal团队自研开发的工具包，我们使用可编辑模式将其安装到虚拟环境中。

```
# 在你想用的虚拟环境中进行, e.g. 我们在nl2sql虚拟环境下，也在nl2sql文件夹里
# higoalutils文件夹中应该有pyproject.toml
# uv 安装
uv pip install -e ../higoalutils
```
### 3. quick start to Alembic
根据我们的toml file, 你的虚拟环境中应该已经安装好了Alembic.
alembic可以管理数据库，从本地csv迁移到数据库，并且有version control.
每当新的version迁移，在alembic/version/里面都会有一个 xxxx_version_name.py
当使用更新命令 ***alembic upgrade head*** 时，有点像github的pull，会依次pull所有版本以保证保存了历史，可以回溯。
更多内容请参考 [quick_start_alembic.md](./quick_start_alembic.md)