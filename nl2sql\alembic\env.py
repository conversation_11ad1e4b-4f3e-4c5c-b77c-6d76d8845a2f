import sys
import os


from logging.config import fileConfig

from sqlalchemy import create_engine, pool
from sqlalchemy import pool

from alembic import context


from higoalutils.utils.config_utils.findpath import get_repo_root
from higoalutils.config.load_config import get_config
# Locate the folder containing your .gitignore
PROJECT_ROOT = get_repo_root()  
os.chdir(PROJECT_ROOT)# auto-detects the repo root
NL2SQL_DIR    = PROJECT_ROOT / "nl2sql"
sys.path[:0] = [str(PROJECT_ROOT), str(NL2SQL_DIR)]

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
cfg = get_config().database_config.mysql_config
# set local_infile=1 to allow loading local data files
db_url = f"mysql+pymysql://{cfg.user}:{cfg.password}@{cfg.host}:{cfg.port}/{cfg.database}?local_infile=1"
config = context.config
config.set_main_option("sqlalchemy.url", db_url)
# Interpret the config file for Python logging.
# This line sets up loggers basically.
ini_path = NL2SQL_DIR / "alembic.ini"
if ini_path.is_file():
    fileConfig(str(ini_path))

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = None

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' (SQL script) mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' (DB) mode via a sync Engine."""
    connectable = create_engine(
        db_url,
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            # optionally:
            # compare_type=True,
            # render_as_batch=True,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
