"""initial schema

Revision ID: 0001_initial
Revises: 
Create Date: 2025-07-12 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect
from nl2sql.config.load_nl2sql_config import get_nl2sql_config

# revision identifiers, used by Alembic.
revision = "0001_initial"
down_revision = None
branch_labels = None
depends_on = None
cfg = get_nl2sql_config().ex_config
# Table paths:
exchange_csv = f'{cfg.data_csv_path}/exchange.csv'
window_symbol_map_csv = f'{cfg.data_csv_path}/window_symbol_map.csv'
symbol_csv = f'{cfg.data_csv_path}/symbol.csv'
trading_window_csv = f'{cfg.data_csv_path}/trading_window.csv'


def upgrade():
    conn = op.get_bind()
    insp = inspect(conn)

    # 1) Create tables if they don't already exist
    if not insp.has_table("exchange"):
        op.create_table(
            "exchange",
            sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
            sa.Column("code", sa.String(10), nullable=False, unique=True),
            sa.Column("name", sa.String(100), nullable=False),
            mysql_engine="InnoDB",
        )

    if not insp.has_table("symbol"):
        op.create_table(
            "symbol",
            sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
            sa.Column("exchange_id", sa.Integer, sa.ForeignKey("exchange.id"), nullable=False),
            sa.Column("prefix", sa.String(20), nullable=False),
            sa.Column("description", sa.String(100), nullable=False),
            sa.Column("special", sa.Boolean, nullable=False, server_default=sa.text("0")),
            sa.UniqueConstraint("exchange_id", "prefix"),
            mysql_engine="InnoDB",
        )

    if not insp.has_table("trading_window"):
        op.create_table(
            "trading_window",
            sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
            sa.Column("exchange_id", sa.Integer, sa.ForeignKey("exchange.id"), nullable=False),
            sa.Column("session", sa.String(20), nullable=False),
            sa.Column("start_time", sa.Time, nullable=False),
            sa.Column("end_time", sa.Time, nullable=False),
            mysql_engine="InnoDB",
        )

    if not insp.has_table("window_symbol_map"):
        op.create_table(
            "window_symbol_map",
            sa.Column("window_id", sa.Integer, sa.ForeignKey("trading_window.id"), primary_key=True),
            sa.Column("prefix", sa.String(20), primary_key=True),
            mysql_engine="InnoDB",
        )



    # 2) Seed data via LOAD DATA
    conn.execute(sa.text("""
      LOAD DATA LOCAL INFILE :f
      INTO TABLE exchange
      FIELDS TERMINATED BY ',' IGNORE 1 LINES
      (code, name)
    """), {"f": exchange_csv})

    conn.execute(sa.text("""
      LOAD DATA LOCAL INFILE :f
      INTO TABLE symbol
      FIELDS TERMINATED BY ',' IGNORE 1 LINES
      (@exchange_code, prefix, description, special)
      SET exchange_id = (SELECT id FROM exchange WHERE code=@exchange_code)
    """), {"f": symbol_csv})

    conn.execute(sa.text("""
      LOAD DATA LOCAL INFILE :f
      INTO TABLE trading_window
      FIELDS TERMINATED BY ',' IGNORE 1 LINES
      (@wid, @exch, session, @st, @et)
      SET
        id = @wid,
        exchange_id = (SELECT id FROM exchange WHERE code=@exch),
        start_time = @st,
        end_time = @et
    """), {"f": trading_window_csv})

    conn.execute(sa.text("""
      LOAD DATA LOCAL INFILE :f
      INTO TABLE window_symbol_map
      FIELDS TERMINATED BY ',' IGNORE 1 LINES
      (window_id, prefix)
    """), {"f": window_symbol_map_csv})



def downgrade():
    # drop in reverse dependency order
    op.drop_table("window_symbol_map")
    op.drop_table("trading_window")
    op.drop_table("symbol")
    op.drop_table("exchange")
