# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""创建pipeline_reporter."""

from higoalutils.callbacks.console_workflow_callbacks import ConsoleWorkflowCallbacks
from higoalutils.callbacks.file_workflow_callbacks import FileWorkflowCallbacks
from higoalutils.callbacks.workflow_callbacks import WorkflowCallbacks
from higoalutils.config.enums import ReportingType
from higoalutils.config.models.reporting_config import ReportingConfig


def create_pipeline_reporter(
    config: ReportingConfig,
    time_zone: str
) -> WorkflowCallbacks:
    """Create a logger for the given pipeline config."""
    match config.type:
        case ReportingType.file:
            return FileWorkflowCallbacks(report_config=config, time_zone=time_zone)
        case ReportingType.console:
            return ConsoleWorkflowCallbacks()
        case _:
            msg = f"Unknown reporting type: {config.type}"
            raise ValueError(msg)
