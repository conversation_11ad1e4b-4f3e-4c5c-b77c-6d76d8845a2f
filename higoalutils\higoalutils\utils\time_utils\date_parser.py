import re
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import List, Dict
import yaml
import os
import calendar
from string import Template

from higoalutils.utils.time_utils.date_uitility import DateUtility


class DateParser:
    def __init__(self):
        self.RESULT_KEYS = [
            "type", "fromDate", "toDate", "fromMonth", "toMonth",
            "fromYear", "toYear", "yoy", "pop", "origin_text"
        ]
        self.query = ""
        self.patterns: List[str] = []
        self.patterns_subsequent: List[str] = []
        self.patterns_definition: Dict[str, callable] = {}
        self.results = []
        self.baseDate = datetime.now()
        BASE_DIR = os.path.dirname(__file__)
        self.yaml_config_patterns_file = os.path.join(BASE_DIR, "rules", "regex_config_patterns.yaml")
        self.yaml_config_rules_file = os.path.join(BASE_DIR, "rules", "regex_config_rules.yaml")
        self._init_patterns()
    
    def _init_patterns(self):
        """初始化所有正则模式"""
        self._load_predefined_rules() 
        self._load_patterns() 
        # self._define_base_date_patterns()
        # self.patterns.extend(self.patterns_subsequent) # 将非优先级的模式加到优先级模式当中去

    def _load_predefined_rules(self):
        # 读取 YAML 文件
        with open(self.yaml_config_rules_file, "r", encoding="utf-8") as file:
            regex_data = yaml.safe_load(file)
        # 遍历 YAML 内容，构建 patterns 字典
        for item in regex_data:
            regex_detail = item.get("regex_detail","")
            regex_rule_name = item.get('regex_rule_name',"").strip().replace("$", "").replace("{","").replace("}", "")
            detail_rules = item.get('detail_rules',{})
            if detail_rules:
                setattr(self, regex_rule_name, {key: int(value) for key, value in detail_rules.items()})
            else:
                setattr(self, regex_rule_name, regex_detail.strip().replace("\n", "").replace("{{", "{").replace("}}", "}"))

    def _load_patterns(self):
        """读取Regex Patterns Configure YAML 文件"""
        with open(self.yaml_config_patterns_file, "r", encoding="utf-8") as file:
            regex_data = yaml.safe_load(file)
        # 遍历 YAML 内容，构建 patterns 字典
        for item in regex_data:
            regex_detail = item.get('regex_detail',"")
            parameters = item.get('parameters',{})
            call_function_name = item.get('call_function',"")
            # 使用 globals() 获取函数对象
            # 下面这段代码先不要删除，globals()可以调公有函数
            if call_function_name in globals():
                call_function = globals()[call_function_name]
            elif call_function_name in locals():
                call_function = locals()[call_function_name]
            else:
                # 使用 getattr(self, call_function_name) 获取类实例中的方法
                call_function = getattr(self, call_function_name, None)
                if call_function is None:
                    raise ValueError(f"date_parser._define_hybrid_patterns Function '{call_function_name}' not found in the scope!")
        
            # 用正则表达式作为 key
            template_str = regex_detail.strip().replace("\n", "").replace("{{", "{").replace("}}", "}")
            
            # 使用类属性字典进行替换
            substituted_str = Template(template_str).safe_substitute(self.__dict__)

            # 编译正则表达式
            key = re.compile(substituted_str)

            # 创建 handler lambda 函数
            # 此处的传递参数要改 和 process_matches函数参数要对上
            handler = lambda m, origin_text, params=parameters, func=call_function: func(m, origin_text, params)
            # 将 key 和 handler 存入 patterns 字典
            self.patterns_definition[key] = {"handler":handler, "parameters":parameters}
            self.patterns.append(key)

    def _handle_absolute_day(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """绝对日期的处理"""
        oper_type = parameters.get("type", "")
        time_unit = parameters.get("time_unit","day")
        offset_type = parameters.get("offset","now")
        if not oper_type or oper_type != 'absolute':
            raise ValueError("date_paser._handle_absolute_day the regex parameter type defined wrong value, it is absolute?")
        base_date = self._func_get_base_date(offset_type)
        if time_unit == "week":
            count_changing = self.PATTERN_ZH_OFFSET.get(m.group(int(parameters.get("match_group",{}).get("week",""))))
            day = base_date + relativedelta(weeks=count_changing)
            result_date = (day - timedelta(days=day.weekday()) + timedelta(days=self._func_cn_to_arabic(m.group(int(parameters.get("match_group",{}).get("day","")))) -1)).strftime("%Y-%m-%d")
        elif parameters.get("match_group",{}).get("multiDay",""):
            day_str = m.group(int(parameters.get("match_group",{}).get("multiDay",""))).replace("日","").strip()
            day_strings = [s for s in re.split(self.PATTERN_SEPARATOR,day_str) if s]
            results = []
            for day_str in day_strings:
                result_date = datetime(base_date.year, base_date.month, self._func_cn_to_arabic(day_str)).strftime("%Y-%m-%d")
                results.append(self._func_format_date_range({"origin_text": origin_text, "type": "single_day", "fromDate": result_date, "toDate": result_date}))
            return results
        else:
            result_date = self._func_single_day(m, parameters)
        return[self._func_format_date_range({"origin_text": origin_text, "type": "single_day", "fromDate": result_date, "toDate": result_date})]
    
    def _handle_range_day(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """日期段的处理"""
        oper_type = parameters.get("type", "")
        time_unit = parameters.get("time_unit","day")
        offset_type = parameters.get("offset","now")
        if not oper_type or oper_type != 'range':
            raise ValueError("date_paser._handle_absolute_day the regex parameter type defined wrong value, it is range?")
        # keyword场景，之前三个月等

        if parameters.get("match_group",{}).get("keyword",""):
            from_result_date, to_result_date = self._func_process_keyword(m, parameters)
        elif time_unit == "week":
            count_changing = self.PATTERN_ZH_OFFSET.get(m.group(int(parameters.get("match_group",{}).get("week",""))))
            base_date = self._func_get_base_date(offset_type)
            day = base_date + relativedelta(weeks=count_changing)
            from_result_date, to_result_date = self._func_time_unit_output(time_unit, day, day)
        else:
            from_parameters = {
                "offset": parameters.get("offset","now"),
                "time_unit": parameters.get("time_unit","day"),
                "match_group": {
                    "year": int(parameters["match_group"].get("fromYear","-1")),
                    "month": int(parameters["match_group"].get("fromMonth","-1")),
                    "day": int(parameters["match_group"].get("fromDay","-1"))
                }
            }
            from_result_date = self._func_single_day(m, from_parameters)
            to_parameters = {
                "offset": parameters.get("offset","now"),
                "time_unit": parameters.get("time_unit","day"),
                "match_group": {
                    "year": int(parameters["match_group"].get("toYear","-1")),
                    "month": int(parameters["match_group"].get("toMonth","-1")),
                    "day": int(parameters["match_group"].get("toDay","-1"))
                }
            }
            to_result_date = self._func_single_day(m, to_parameters)
        return[self._func_format_date_range({"origin_text": origin_text, "type": "range_day", "fromDate": from_result_date, "toDate": to_result_date})]
    
    def _handle_absolute_month(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """绝对日期的处理"""
        oper_type = parameters.get("type", "")
        offset_type = parameters.get("offset", "now")
        date_param = parameters.get("match_group",{})
        if not oper_type or oper_type != 'absolute':
            raise ValueError("date_paser._handle_absolute_month the regex parameter type defined wrong value, it is absolute?")
        if date_param.get("year",-1) == -1:
            year_int = 0
        else:
            year_int = self._func_get_year(m.group(int(date_param["year"])), offset_type)
        
        tmp_int_1,month_int = self._func_get_month(m.group(int(date_param["month"])), offset_type)
        if year_int == 0 and tmp_int_1 !=0:
            year_int = tmp_int_1

        month_str = f"{(datetime(year_int, month_int, 1).strftime('%Y-%m'))}"
        return[self._func_format_date_range({"origin_text": origin_text, "type": "single_month", "fromMonth": month_str, "toMonth": month_str})]

    def _handle_range_month(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """绝对日期的处理"""
        oper_type = parameters.get("type", "range")
        offset_type = parameters.get("offset", "now")
        time_unit = parameters.get("time_unit", "month")
        date_param = parameters.get("match_group",{})
        if oper_type != 'range':
            raise ValueError("date_paser._handle_absolute_month the regex parameter type defined wrong value, it is range?")

        # keyword场景，之前三个月等
        if date_param.get("keyword",""):
            fromMonth, toMonth = self._func_process_keyword(m, parameters)
        elif time_unit == "quarter":
            count_changing = self.PATTERN_ZH_OFFSET.get(m.group(int(date_param.get("quarter",""))))
            absolute_count = self._func_cn_to_arabic(m.group(int(date_param.get("quarter",""))))
            base_date = self._func_get_base_date(offset_type)
            if date_param.get("year",""):
                from_year_int = self._func_get_year(m.group(int(date_param.get("year"))), offset_type)
                if from_year_int != 0:
                    base_date = datetime(from_year_int, 1, 1)
            if count_changing:
                day = base_date + relativedelta(months=count_changing*3)
            else:
                day = base_date.replace(month=1,day=1) + relativedelta(months=absolute_count*3-1) # 小坑，relativedelta()是在base_date上顺加数字，即原来是1月+3季度（3*3）=10月
            fromMonth, toMonth = self._func_time_unit_output(time_unit, day, day)
        else:
            if date_param.get("fromYear",-1) == -1:
                from_year_int = 0
            else:
                from_year_int = self._func_get_year(m.group(int(date_param["fromYear"])), offset_type)
            
            if date_param.get("fromMonth",-1) == -1:
                from_month_int = 0
            else:
                tmp_int_1,from_month_int = self._func_get_month(m.group(int(date_param["fromMonth"])), offset_type)
                if from_year_int == 0 and tmp_int_1 !=0:
                    from_year_int = tmp_int_1
            
            if date_param.get("toYear",-1) == -1:
                to_year_int = 0
            else:
                to_year_int = self._func_get_year(m.group(int(date_param["toYear"])), offset_type)
            
            if date_param.get("toMonth",-1) == -1:
                to_month_int = 0
            else:
                tmp_int_1,to_month_int = self._func_get_month(m.group(int(date_param["toMonth"])), offset_type)
                if to_year_int == 0 and tmp_int_1 !=0:
                    to_year_int = tmp_int_1
            fromMonth = f"{(datetime(from_year_int, from_month_int, 1).strftime('%Y-%m'))}"
            toMonth = f"{(datetime(to_year_int, to_month_int, 1).strftime('%Y-%m'))}"

        return[self._func_format_date_range({"origin_text": origin_text, "type": "range_month", "fromMonth": fromMonth, "toMonth": toMonth})]
    
    def _handle_special_month(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """中文的特殊语境规则，包括 XX年前3个月，指的是1-3月，格式适配"""
        oper_type = parameters.get("type", "")
        offset_type = parameters.get("offset", "now")
        date_param = parameters.get("match_group",{})
        if not oper_type or oper_type != 'range':
            raise ValueError("date_paser._handle_special_month the regex parameter type defined wrong value, it is range?")
        if date_param.get("year",-1) == -1:
            raise ValueError("date_paser._handle_special_month the regex parameter miss \'year\' parameter")
        else:
            year_int = self._func_get_year(m.group(int(date_param["year"])), offset_type)
        
        direction = m.group(int(date_param.get("direction","")))
        count_changing = self._func_cn_to_arabic(m.group(int(date_param.get("month","0"))))
        if direction == "前":
            fromMonth = f"{(datetime(year_int, 1, 1).strftime('%Y-%m'))}"
            toMonth = f"{(datetime(year_int, count_changing, 1).strftime('%Y-%m'))}"
        else:
            fromMonth = f"{(datetime(year_int, 12-count_changing+1, 1).strftime('%Y-%m'))}"
            toMonth = f"{(datetime(year_int, 12, 1).strftime('%Y-%m'))}"

        return[self._func_format_date_range({"origin_text": origin_text, "type": "range_month", "fromMonth": fromMonth, "toMonth": toMonth})]
    
    def _handle_absolute_year(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """绝对年份的处理"""
        oper_type = parameters.get("type", "")
        offset_type = parameters.get("offset", "now")
        date_param = parameters.get("match_group",{})
        if not oper_type or oper_type != 'absolute':
            raise ValueError("date_paser._handle_absolute_year the regex parameter type defined wrong value, it is absolute?")
        if date_param.get("year",-1) == -1:
            year_int = 0
        else:
            year_int = self._func_get_year(m.group(int(date_param["year"])), offset_type)

        year_str = f"{(datetime(year_int, 1, 1).strftime('%Y'))}"
        return[self._func_format_date_range({"origin_text": origin_text, "type": "single_year", "fromYear": year_str, "toYear": year_str})]
    
    def _handle_range_year(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """绝对年份的处理"""
        oper_type = parameters.get("type", "")
        offset_type = parameters.get("offset", "now")
        date_param = parameters.get("match_group",{})
        if not oper_type or oper_type != 'range':
            raise ValueError("date_paser._handle_absolute_year the regex parameter type defined wrong value, it is range?")
        
        from_year_int = self._func_get_year(m.group(int(date_param.get("fromYear"))), offset_type)
        to_year_int = self._func_get_year(m.group(int(date_param.get("toYear"))), offset_type)

        return[self._func_format_date_range({"origin_text": origin_text, "type": "range_year", "fromYear": f"{(datetime(from_year_int, 1, 1).strftime('%Y'))}", "toYear": f"{(datetime(to_year_int, 1, 1).strftime('%Y'))}"})]
    def _handle_range_previous_condition(self, m, origin_text: str, parameters:Dict) -> List[Dict]:
        """特殊日期段的处理，根据中文语言习惯，X年X月X日至次年XX月X日，做的特殊化， 即第二个日期前置依赖第一个日期"""
        oper_type = parameters.get("type", "")
        time_unit = parameters.get("time_unit","day")
        offset_type = parameters.get("offset","now")
        if not oper_type or oper_type != 'range':
            raise ValueError("date_paser._handle_absolute_day the regex parameter type defined wrong value, it is range?")
        # keyword场景，之前三个月等
        if time_unit == "day" and parameters.get("match_group",{}).get("toDay",""): # 要求是 XX日期 -  XX日期格式
            from_parameters = {
                "offset": parameters.get("offset","now"),
                "time_unit": parameters.get("time_unit","day"),
                "match_group": {
                    "year": int(parameters["match_group"].get("fromYear","-1")),
                    "month": int(parameters["match_group"].get("fromMonth","-1")),
                    "day": int(parameters["match_group"].get("fromDay","-1"))
                }
            }
            from_result_date = self._func_single_day(m, from_parameters)
            base_date = self.baseDate
            self.baseDate = datetime(int(from_result_date.split("-")[0]), int(from_result_date.split("-")[1]), int(from_result_date.split("-")[2]))
            to_parameters = {
                "offset": parameters.get("offset","now"),
                "time_unit": parameters.get("time_unit","day"),
                "match_group": {
                    "year": int(parameters["match_group"].get("toYear","-1")),
                    "month": int(parameters["match_group"].get("toMonth","-1")),
                    "day": int(parameters["match_group"].get("toDay","-1"))
                }
            }
            to_result_date = self._func_single_day(m, to_parameters)
            self.baseDate = base_date
        return[self._func_format_date_range({"origin_text": origin_text, "type": "range_day", "fromDate": from_result_date, "toDate": to_result_date})]
    
    def _handle_yoy_pop(self, m, origin_text: str, parameters:Dict):
        """处理单个匹配"""
        if self.results: # 利用上下文补全年份
            for result in self.results: # 靠左原则，从近往远取
                if result.get("type") == "single_day":
                    fromDate = datetime(int(result.get("fromDate", "").split("-")[0]),int(result.get("fromDate", "").split("-")[1]),int(result.get("fromDate", "").split("-")[2]))
                    toDate = fromDate
                elif result.get("type") == "day_range":
                    fromDate = datetime(int(result.get("fromDate", "").split("-")[0]),int(result.get("fromDate", "").split("-")[1]),int(result.get("fromDate", "").split("-")[2]))
                    toDate = datetime(int(result.get("toDate", "").split("-")[0]),int(result.get("toDate", "").split("-")[1]),int(result.get("toDate", "").split("-")[2]))
                elif result.get("type") == "single_month":
                    fromDate = datetime(int(result.get("fromMonth", "").split("-")[0]),int(result.get("fromMonth", "").split("-")[1]),1)
                    toDate = datetime(int(result.get("toMonth", "").split("-")[0]),int(result.get("toMonth", "").split("-")[1]),1)
                elif result.get("type") == "month_range":
                    fromDate = datetime(int(result.get("fromMonth", "").split("-")[0]),int(result.get("fromMonth", "").split("-")[1]),1)
                    toDate = datetime(int(result.get("toMonth", "").split("-")[0]),int(result.get("toMonth", "").split("-")[1]),1)
                elif result.get("type") == "single_year":
                    fromDate = datetime(int(result.get("fromYear", "")), 1, 1)
                    toDate = datetime(int(result.get("toYear", "")), 1, 1)
                else:
                    continue # 即，range_year 不做同比、环比处理
                # 利用python的dict特性，用指针写回内存块
                if origin_text == "同比":
                    if result.get("type") == "single_day" or result.get("type") == "day_range":
                        if fromDate.year == toDate.year:
                            result["yoy"] = {"fromDate": (fromDate - relativedelta(years=1)).strftime("%Y-%m-%d"), "toDate": (toDate - relativedelta(years=1)).strftime("%Y-%m-%d")}
                    if result.get("type") == "single_month" or result.get("type") == "month_range":
                        if fromDate.year == toDate.year:
                            result["yoy"] = {"fromMonth": (fromDate - relativedelta(years=1)).strftime("%Y-%m"), "toMonth": (toDate - relativedelta(years=1)).strftime("%Y-%m")}
                    if result.get("type") == "single_year":
                        result["yoy"] = {"fromYear": (fromDate-relativedelta(years=1)).strftime("%Y"), "toYear": (toDate-relativedelta(years=1)).strftime("%Y")}
                elif origin_text == "环比" or origin_text == "月环比":
                    if result.get("type") == "single_day" or result.get("type") == "day_range":
                        if fromDate.strftime('%Y-%m') == toDate.strftime('%Y-%m'): # 同一个月内才有环比
                            result["pop"] = {"fromDate": (fromDate-relativedelta(months=1)).strftime("%Y-%m-%d"), "toDate": (toDate-relativedelta(months=1)).strftime("%Y-%m-%d")}
                    if result.get("type") == "single_month": # 多个月不会有环比
                        result["pop"] = {"fromDate": (fromDate-relativedelta(months=1)).strftime("%Y-%m"), "toDate": (fromDate-relativedelta(months=1)).strftime("%Y-%m")}
                else: # 周环比，仅单天有周环比
                    if result.get("type") == "single_day":
                        fromDate_pop = fromDate - relativedelta(weeks=1) - relativedelta(days=fromDate.weekday())
                        toDate_pop = fromDate_pop + relativedelta(days=6)
                        result["pop"] = {"fromDate": fromDate_pop.strftime("%Y-%m-%d"), "toDate": toDate_pop.strftime("%Y-%m-%d")}
        return [] # 给上层函数一个交代
    def _func_process_keyword(self, m, parameters:Dict) -> tuple[str,str]:
        date_param = parameters.get("match_group",{})
        offset_type = parameters.get("offset", "now")
        time_unit = parameters.get("time_unit","day")
        direction = self.PATTERN_ZH_DIRECTION.get(m.group(int(date_param["keyword"])), 0)
        countChange = direction * self._func_cn_to_arabic(m.group(int(date_param.get("offset",0))))
        base_date = self._func_get_base_date(offset_type)
        if time_unit == "day":
            offset_days = relativedelta(days=countChange)
            offset_next_day =  relativedelta(days=direction*1)
        elif time_unit == "week":
            offset_days = relativedelta(weeks=countChange)
            offset_next_day =  relativedelta(weeks=direction*1)
        elif time_unit == "month":
            offset_days = relativedelta(months=countChange)
            offset_next_day =  relativedelta(months=direction*1)
        elif time_unit == "quarter":
            offset_days = relativedelta(months=countChange*3)
            offset_next_day =  relativedelta(months=direction*3)
        elif time_unit == "year":
            offset_days = relativedelta(years=countChange)
            offset_next_day =  relativedelta(years=direction*1)

        result_date = base_date + offset_days
        if direction < 0: # 取前几个月的情况\
            from_result_date = result_date
            to_result_date = (base_date + offset_next_day) # 结束月是相对日期的前一个月
        else: # 取后几个月的情况
            from_result_date = (base_date + offset_next_day)  # 结束月是相对日期的前一个月
            to_result_date = result_date # 结束月是相对日期的前一个月
    
        # 输出格式
        from_result_str, to_result_str = self._func_time_unit_output(time_unit, from_result_date, to_result_date)
        return from_result_str, to_result_str
    def _func_time_unit_output(self, time_unit:str, from_result_date: datetime, to_result_date: datetime) -> tuple[str,str]:
        # 输出格式
        if time_unit == "day":
            from_result_str = from_result_date.strftime("%Y-%m-%d")
            to_result_str = to_result_date.strftime("%Y-%m-%d")
        elif time_unit == "week":
            from_result_str = (from_result_date - relativedelta(days=from_result_date.weekday())).strftime("%Y-%m-%d")
            to_result_str = (to_result_date -relativedelta(days=to_result_date.weekday()) + relativedelta(days=6)).strftime("%Y-%m-%d")
        elif time_unit == "month":
            from_result_str = from_result_date.strftime("%Y-%m")
            to_result_str = to_result_date.strftime("%Y-%m")
        elif time_unit == "quarter":
            year = from_result_date.year
            quarter = from_result_date.month // 3 + (1 if from_result_date.month % 3 !=0 else 0)
            from_result_date = datetime(year, quarter*3-2,1) # 季初
            from_result_str = from_result_date.strftime("%Y-%m")
            year = to_result_date.year
            quarter = to_result_date.month // 3 + (1 if to_result_date.month % 3 !=0 else 0)
            to_result_date = datetime(year if quarter<4 else year+1, (quarter*3+1) if quarter <4 else 1, 1) - timedelta(days=1) # 季末
            to_result_str = to_result_date.strftime("%Y-%m")
        elif time_unit == "year":
            from_result_str = from_result_date.strftime("%Y")
            to_result_str = to_result_date.strftime("%Y")
        return from_result_str, to_result_str

    def _func_single_day(self, m, parameters:Dict) -> str:
        """处理单日期的处理"""
        offset_type = parameters.get("offset", "now")
        date_param = parameters.get("match_group",{})
        if date_param.get("year",-1) == -1:
            year_int = 0
        else:
            year_int = self._func_get_year(m.group(int(date_param["year"])), offset_type)

        if date_param.get("month",-1) == -1:
            month_int = 0
        else:
            tmp_int_1,month_int = self._func_get_month(m.group(int(date_param["month"])), offset_type)
            if year_int == 0 and tmp_int_1 !=0:
                year_int = tmp_int_1
        if date_param.get("day",-1) == -1:
            day_int = 0
        else:
            tmp_int_1,tmp_int_2,day_int = self._func_get_day(m.group(int(date_param["day"])), offset_type)
            if month_int == 0:
                month_int = tmp_int_2
            if year_int == 0 and tmp_int_1 !=0: # 匹配到月、日
                year_int = tmp_int_1
        if year_int != 0:
            result_date = datetime(year_int, month_int, day_int).strftime("%Y-%m-%d")
        else:
            result_date = self._func_complete_date(f"{month_int}月{day_int}日")
        return result_date
        
    def _func_get_base_date(self, offset_type="now") -> datetime:
        if offset_type == "now":
            base_date = self.baseDate
        else:
            year_int, month_int, day_int = self._func_get_context_date("YYYY-MM-DD")
            if year_int == 0:
                base_date = self.baseDate
            else:
                base_date = datetime(year_int, month_int, day_int)
        return base_date
        
    def _func_get_year(self, year_str, offset_type="now") -> int:
        """获取年份"""
        year = self._func_cn_to_arabic(year_str)
        if year == 0: # 如果是零，则用ZH规则处理（指去年、明年等等中文标记法），用偏移量计算年份
            countChange = self.PATTERN_ZH_OFFSET.get(year_str, 0)
            if offset_type == "now":
                year = (self.baseDate + relativedelta(years = countChange)).year
            else:
                year, tmp_int_1, tmp_int_2 = self._func_get_context_date("YYYY")
                if year == 0: # 没有取到年
                    year = self.baseDate.year
                year = (self.baseDate.replace(year=year) + relativedelta(years = countChange)).year
        return year
    
    def _func_get_month(self, month_str, offset_type="now") -> tuple[int, int]:
        """获取月份"""
        month = self._func_cn_to_arabic(month_str)
        year = 0
        if month == 0: # 如果是零，则用ZH规则处理（指去年、明年等等中文标记法），用偏移量计算月份
            countChange = self.PATTERN_ZH_OFFSET.get(month_str, 0)
            if offset_type == "now":
                result_date = self.baseDate + relativedelta(months = countChange)
            else:
                year, month, tmp_int_2 = self._func_get_context_date("YYYY-MM")
                if year == 0: # 没有取到年
                    year = self.baseDate.year
                if month == 0: # 没有取到月
                    month = self.baseDate.month
                result_date = self.baseDate.replace(year=year).replace(month=month) + relativedelta(months = countChange)
            month = result_date.month
            year = result_date.year
        return year,month
    
    def _func_get_day(self, day_str, offset_type="now") -> tuple[int, int, int]:
        """获取日期"""
        day = self._func_cn_to_arabic(day_str)
        month = 0
        year = 0
        if day == 0: # 如果是零，则用ZH规则处理（指去年、明年等等中文标记法），用偏移量计算日期
            countChange = self.PATTERN_ZH_OFFSET.get(day_str, 0)
            if offset_type == "now":
                result_date = self.baseDate + relativedelta(days = countChange)
            else:
                year, month, day = self._func_get_context_date("YYYY-MM-DD")
                if year == 0: # 没有取到年
                    result_date = self.baseDate + relativedelta(days = countChange)
                else:
                    result_date = datetime(year, month, day) + relativedelta(days = countChange)
            day = result_date.day
            month = result_date.month
            year = result_date.year
        return year, month, day
    
    def _func_get_context_date(self, category="YYYY") -> tuple[int, int, int]:
        """取上下文当中的年份、月份、日期"""
        year = 0
        month = 0
        day = 0
        if self.results: # 利用上下文补全年份
            for result in self.results[::-1]: # 靠左原则，从近往远取
                if result.get("type") == "single_day":
                    year = int(result.get("fromDate", "").split("-")[0])
                    month = int(result.get("fromDate", "").split("-")[1])
                    day = int(result.get("fromDate", "").split("-")[2])
                elif result.get("type") == "day_range":
                    from_year = result.get("fromDate", "").split("-")[0]
                    to_year = result.get("toDate", "").split("-")[0]
                    if from_year == to_year: # 如果上下文是同一年的，则使用上下文年份
                        year = int(to_year)
                        month = int(result.get("toDate", "").split("-")[1])
                        day = int(result.get("toDate", "").split("-")[2])
                elif result.get("type") == "single_month":
                    year = int(result.get("fromMonth", "").split("-")[0])
                    month = int(result.get("fromMonth", "").split("-")[1])
                    day = 1
                elif result.get("type") == "month_range":
                    from_year = result.get("fromMonth", "").split("-")[0]
                    to_year = result.get("toMonth", "").split("-")[0]
                    if from_year == to_year: # 如果上下文是同一年的，则使用上下文年份
                        year = int(to_year)
                        month = int(result.get("toMonth", "").split("-")[1])
                elif result.get("type") == "single_year":
                    year = int(result.get("fromYear", ""))

                if category == "YYYY" and year != 0:
                    return year,0,0
                elif category == "YYYY-MM" and year != 0 and month != 0:
                    return year, month, 0
                elif category == "YYYY-MM-DD" and year != 0 and month != 0 and day != 0:
                    return year, month, day
        return year,month,day
    def _func_complete_date(self, part_date_str) -> int:
        """只有月日的日期，利用上下文，补足年份"""
        if "月" not in part_date_str or "日" not in part_date_str: 
            raise ValueError("date_paser._func_complete_date the part_date_str is invalid, it must like '02月03日'")# 如果没有月和日，报错
        year, tmp_int_1, tmp_int_2 = self._func_get_context_date('YYYY')
        if year != 0:
            return datetime(year,int(part_date_str.replace("月", "-").replace("日", "").split("-")[0]),int(part_date_str.replace("月", "-").replace("日", "").split("-")[1])).strftime("%Y-%m-%d")
        
        result_date = DateUtility.format_date(part_date_str) # 如果没有上下文的情况，尝试使用DateUtility.format_date, 这个函数会判断离当前最近的一个月份（比如，1月份问12月份，会给出上年12月份）
        return result_date
    def _func_format_date_range(self, partial: Dict) -> Dict:
        """统一格式化输出"""
        return {key: partial.get(key, None) for key in self.RESULT_KEYS}
    
    def _func_cn_to_arabic(self, cn_num: str) -> int:
        """优化后的中文数字转换"""
        cn_num = cn_num.replace(" ","").strip()
        try:
            return int(cn_num)
        except ValueError:
            pass

        total = 0
        temp = 0
        # 只支持 一九二四 、九八七年、四九年 这几种特殊的中文年份格式处理
        if len(cn_num) >= 2 and ('十' not in cn_num and '百' not in cn_num and "千" not in cn_num and '万' not in cn_num): 
            i = 10
            for char in cn_num:
                val = self.CN_NUM_MAP.get(char, 0)
                total =  total * i + val
            return total
        
        for char in cn_num:
            val = self.CN_NUM_MAP.get(char, 0)
            if val == 10:  # 处理"十"
                total += temp * val if temp > 0 else 10
                temp = 0
            elif val >= 100: # 处理"百、千、万"
                total += temp * val
                temp = 0
            else:
                temp += val
        return total + temp
    
    def process_matches(self, query: str, base_date: datetime = datetime.now()):
        self.query = query
        self.baseDate = base_date
        processed = set()

        for pattern in self.patterns:
            for match in re.finditer(pattern, self.query):
                if (text := match.group(0)) not in processed:
                    self.results.extend(self._process_match(pattern, match))
                    processed.add(text)
                    self.query = self.query.replace(text, "")
        return self.results

    def _process_match(self, pattern: str, match: re.Match) -> List[Dict]:
        """处理单个匹配"""
        try:
            handler = self.patterns_definition[pattern].get("handler","")
            parameters = self.patterns_definition[pattern].get("parameters",{})
            if handler is None:
                raise ValueError(f"date_parser._process_match Function the pattern:'{pattern}' corresponding '{handler}' not found in the scope!")

            # 下面的传递参数要改
            result = handler(match, match.group(0), parameters)
            if result:
                return [result] if isinstance(result, dict) else result
            else:
                return []
        except Exception as e:
            print(f"Error processing match: {e}")
            return []
    

import calendar
from typing import Dict


class DateGranularityConverter:
    @staticmethod
    def to_day_granularity(result: Dict) -> Dict:
        main = DateGranularityConverter._convert_single(result, target="day")
        output = main.copy()
        for field in ("yoy", "pop"):
            if field in result and result[field]:
                output[field] = DateGranularityConverter._convert_single(result[field], target="day")
        return output

    @staticmethod
    def to_month_granularity(result: Dict) -> Dict:
        main = DateGranularityConverter._convert_single(result, target="month")
        output = main.copy()
        for field in ("yoy", "pop"):
            if field in result and result[field]:
                output[field] = DateGranularityConverter._convert_single(result[field], target="month")
        return output

    @staticmethod
    def to_year_granularity(result: Dict) -> Dict:
        main = DateGranularityConverter._convert_single(result, target="year")
        output = main.copy()
        for field in ("yoy", "pop"):
            if field in result and result[field]:
                output[field] = DateGranularityConverter._convert_single(result[field], target="year")
        return output

    @staticmethod
    def _convert_single(result: Dict, target: str) -> Dict:
        """内部工具函数：根据目标颗粒度转换一个 dict，支持自动推断类型"""
        if "type" not in result:
            result = result.copy()
            result["type"] = DateGranularityConverter._infer_type(result)

        if target == "day":
            if result["type"] in ("single_day", "range_day"):
                from_day = result["fromDate"]
                to_day = result["toDate"]
            elif result["type"] in ("single_month", "range_month"):
                from_year, from_month = map(int, result["fromMonth"].split("-"))
                to_year, to_month = map(int, result["toMonth"].split("-"))
                from_day = f"{from_year}-{from_month:02d}-01"
                last_day = calendar.monthrange(to_year, to_month)[1]
                to_day = f"{to_year}-{to_month:02d}-{last_day:02d}"
            elif result["type"] in ("single_year", "range_year"):
                from_day = f"{result['fromYear']}-01-01"
                to_day = f"{result['toYear']}-12-31"
            else:
                raise ValueError(f"Unsupported type for day granularity: {result['type']}")
            typ = "single_day" if from_day == to_day else "range_day"
            return {
                "type": typ,
                "fromDate": from_day,
                "toDate": to_day
            }

        elif target == "month":
            if result["type"] in ("single_month", "range_month"):
                from_month = result["fromMonth"]
                to_month = result["toMonth"]
            elif result["type"] in ("single_day", "range_day"):
                from_month = "-".join(result["fromDate"].split("-")[:2])
                to_month = "-".join(result["toDate"].split("-")[:2])
            elif result["type"] in ("single_year", "range_year"):
                from_month = f"{result['fromYear']}-01"
                to_month = f"{result['toYear']}-12"
            else:
                raise ValueError(f"Unsupported type for month granularity: {result['type']}")
            typ = "single_month" if from_month == to_month else "range_month"
            return {
                "type": typ,
                "fromMonth": from_month,
                "toMonth": to_month
            }

        elif target == "year":
            if result["type"] in ("single_year", "range_year"):
                from_year = result["fromYear"]
                to_year = result["toYear"]
            elif result["type"] in ("single_month", "range_month"):
                from_year = result["fromMonth"].split("-")[0]
                to_year = result["toMonth"].split("-")[0]
            elif result["type"] in ("single_day", "range_day"):
                from_year = result["fromDate"].split("-")[0]
                to_year = result["toDate"].split("-")[0]
            else:
                raise ValueError(f"Unsupported type for year granularity: {result['type']}")
            typ = "single_year" if from_year == to_year else "range_year"
            return {
                "type": typ,
                "fromYear": from_year,
                "toYear": to_year
            }

        else:
            raise ValueError(f"Unsupported granularity target: {target}")

    @staticmethod
    def _infer_type(result: Dict) -> str:
        if "fromDate" in result and result["fromDate"]:
            return "single_day" if result.get("fromDate") == result.get("toDate") else "range_day"
        elif "fromMonth" in result and result["fromMonth"]:
            return "single_month" if result.get("fromMonth") == result.get("toMonth") else "range_month"
        elif "fromYear" in result and result["fromYear"]:
            return "single_year" if result.get("fromYear") == result.get("toYear") else "range_year"
        else:
            raise ValueError(f"Cannot infer type from: {result}")
    