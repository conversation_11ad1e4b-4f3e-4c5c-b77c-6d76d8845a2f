# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""时间工具模块."""

import re
from datetime import UTC, date, datetime, timedelta

import pytz
from dateutil.parser import parse as dateutil_parse
from dateutil.relativedelta import relativedelta

from higoalutils.config.enums import TimeGranularity, TimeType
from higoalutils.config.load_config import get_config

TIME_ZONE = get_config().base_config.time_zone
TZ = pytz.timezone(TIME_ZONE)


class TimeUtils:
    """时间工具类."""

    @staticmethod
    def now_utc() -> datetime:
        """获取当前UTC时间."""
        return datetime.now(UTC)

    @staticmethod
    def now_local() -> datetime:
        """获取当前本地时间."""
        return TimeUtils.now_utc().astimezone(TZ)

    @staticmethod
    def to_utc(dt: datetime) -> datetime:
        """转换为UTC时间."""
        if dt.tzinfo is None:
            return dt.replace(tzinfo=TZ).astimezone(UTC)
        return dt.astimezone(UTC)
    

    @staticmethod
    def from_utc_to_readable(utc_str: str, granularity: TimeGranularity = TimeGranularity.SECOND) -> str:
        """从UTC字符串转换为可读格式."""
        try:
            dt = datetime.fromisoformat(utc_str.replace("Z", "+00:00"))
            return TimeUtils.format_with_granularity(dt.astimezone(TZ), granularity)
        except Exception as e:
            error_msg = f"Invalid UTC datetime format: {utc_str}"
            raise ValueError(error_msg) from e

    @staticmethod
    def get_time_bundle(time_type: TimeType = TimeType.UTC) -> str | int:
        """获取时间包."""
        dt = TimeUtils.now_local()
        match time_type:
            case TimeType.UTC:
                return dt.astimezone(UTC).isoformat()
            case TimeType.READABLE:
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            case TimeType.TIMESTAMP:
                return int(dt.timestamp())
            case _:
                error_msg = f"Unsupported time type: {time_type}"
                raise ValueError(error_msg)

    @staticmethod
    def parse_to_utc(value: str | float, kind: TimeType) -> datetime:
        """解析为UTC时间."""
        if kind == TimeType.READABLE:
            return datetime.strptime(str(value), "%Y-%m-%d %H:%M:%S").replace(tzinfo=TZ).astimezone(UTC)
        if kind == TimeType.TIMESTAMP:
            return datetime.fromtimestamp(float(value), tz=UTC)
        if kind == TimeType.UTC:
            return datetime.fromisoformat(str(value).replace("Z", "+00:00")).astimezone(UTC)
        if kind == TimeType.NATURAL:
            return TimeUtils._parse_natural_language(str(value))
        error_msg = f"Unsupported kind: {kind}"
        raise ValueError(error_msg)

    @staticmethod
    def _parse_natural_language(value: str) -> datetime:
        """解析自然语言时间."""
        value = value.strip().lower()
        today = TimeUtils.now_local()

        if value in {"今天", "今日", "now", "today"}:
            return today.astimezone(UTC)
        if value in {"昨天", "yesterday"}:
            return (today - timedelta(days=1)).astimezone(UTC)
        if value in {"前天", "the day before yesterday"}:
            return (today - timedelta(days=2)).astimezone(UTC)
        if "天前" in value or "days ago" in value:
            num = int(re.findall(r"\d+", value)[0])
            return (today - timedelta(days=num)).astimezone(UTC)
        if "月前" in value or "months ago" in value:
            num = int(re.findall(r"\d+", value)[0])
            return (today - relativedelta(months=num)).astimezone(UTC)
        if "年前" in value or "years ago" in value:
            num = int(re.findall(r"\d+", value)[0])
            return (today - relativedelta(years=num)).astimezone(UTC)

        try:
            dt = dateutil_parse(value)
            return TimeUtils.to_utc(dt)
        except Exception as e:
            error_msg = f"Unrecognized natural date string: {value}"
            raise ValueError(error_msg) from e

    @staticmethod
    def format_with_granularity(dt: datetime, granularity: TimeGranularity) -> str:
        """按粒度格式化时间."""
        formats = {
            TimeGranularity.YEAR: "%Y",
            TimeGranularity.MONTH: "%Y-%m",
            TimeGranularity.DAY: "%Y-%m-%d",
            TimeGranularity.HOUR: "%Y-%m-%d %H",
            TimeGranularity.MINUTE: "%Y-%m-%d %H:%M",
            TimeGranularity.SECOND: "%Y-%m-%d %H:%M:%S"
        }
        return dt.strftime(formats[granularity])

    @staticmethod
    def get_now_local_readable(granularity: TimeGranularity = TimeGranularity.SECOND) -> str:
        """获取当前本地可读时间."""
        return TimeUtils.format_with_granularity(TimeUtils.now_local(), granularity)

    @staticmethod
    def is_leap_year(year: int) -> bool:
        """检查是否为闰年."""
        return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)

    @staticmethod
    def get_last_day_of_month(year: int, month: int) -> datetime:
        """获取月份最后一天."""
        if month == 12:
            return datetime(year + 1, 1, 1, tzinfo=UTC) - timedelta(days=1)
        return datetime(year, month + 1, 1, tzinfo=UTC) - timedelta(days=1)

    @staticmethod
    def replace_prompt_dates(prompt: str) -> str:
        """替换提示中的日期占位符."""
        today = TimeUtils.now_local().date()
        year = today.year
        weekday = ["一", "二", "三", "四", "五", "六", "日"][today.weekday()]
        fd_this_month = today.replace(day=1)
        fd_last_month = (fd_this_month - timedelta(days=1)).replace(day=1)
        start_this_week = today - timedelta(days=today.weekday())
        start_last_week = start_this_week - timedelta(days=7)
        date_map = {
            "today": today,
            "year": year,
            "weekday": weekday,
            "yesterday": today - timedelta(days=1),
            "day_before_yesterday": today - timedelta(days=2),
            "this_week_start": start_this_week,
            "this_week_end": today,
            "last_week_start": start_last_week,
            "last_week_end": start_last_week + timedelta(days=6),
            "this_month_start": fd_this_month,
            "this_month_end": today,
            "last_month_start": fd_last_month,
            "last_month_end": fd_this_month - timedelta(days=1),
            "this_year_start": datetime(today.year, 1, 1, tzinfo=UTC).date(),
            "this_year_end": today,
        }
        for key, val in date_map.items():
            prompt = prompt.replace(f"{{{key}}}", val.strftime("%Y-%m-%d") if isinstance(val, (datetime, date)) else str(val))
        return prompt
